#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API端点
"""

import json
import urllib.request
import urllib.parse
import urllib.error

def test_material_detail_route():
    """测试物资详情路由"""
    try:
        print("=== 测试物资详情路由 ===")
        
        # 测试物资ID 17（铅笔）
        material_id = 17
        url = f"http://localhost:5000/materials/{material_id}"
        
        print(f"测试URL: {url}")
        
        try:
            response = urllib.request.urlopen(url)
            status_code = response.getcode()
            print(f"状态码: {status_code}")
            
            if status_code == 200:
                print("✅ 物资详情页面访问成功")
            else:
                print(f"⚠️ 物资详情页面返回状态码: {status_code}")
                
        except urllib.error.HTTPError as e:
            print(f"❌ HTTP错误: {e.code} - {e.reason}")
        except urllib.error.URLError as e:
            print(f"❌ URL错误: {e.reason}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_api_request_route():
    """测试API申请路由"""
    try:
        print("\n=== 测试API申请路由 ===")
        
        # 测试物资ID 17（铅笔）
        material_id = 17
        url = f"http://localhost:5000/api/materials/{material_id}/request"
        
        print(f"测试URL: {url}")
        
        # 准备测试数据
        data = {
            'quantity': 1,
            'reason': '测试申请'
        }
        
        # 由于需要登录，这个测试会失败，但我们可以检查路由是否存在
        try:
            req = urllib.request.Request(
                url,
                data=json.dumps(data).encode('utf-8'),
                headers={'Content-Type': 'application/json'}
            )
            response = urllib.request.urlopen(req)
            status_code = response.getcode()
            print(f"状态码: {status_code}")
            
        except urllib.error.HTTPError as e:
            if e.code == 302:  # 重定向到登录页面
                print("✅ API端点存在（需要登录，返回302重定向）")
            elif e.code == 401:
                print("✅ API端点存在（需要认证，返回401）")
            elif e.code == 404:
                print("❌ API端点不存在（404错误）")
            else:
                print(f"⚠️ API端点返回状态码: {e.code}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_material_detail_route()
    test_api_request_route()
