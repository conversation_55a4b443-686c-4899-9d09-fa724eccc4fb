#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试推荐功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.agent_service import AgentService
from agents.smart_recommendation_agent import SmartRecommendationAgent

def test_complete_recommendation_flow():
    """测试完整的推荐流程"""
    try:
        print("=== 完整推荐功能测试 ===")
        
        # 1. 测试推荐生成
        print("\n1. 测试推荐生成...")
        agent = SmartRecommendationAgent()
        
        # 为管理员生成新推荐
        admin_recs = agent.generate_user_recommendations(1)
        print(f"管理员新推荐数量: {len(admin_recs)}")
        
        if admin_recs:
            agent.save_recommendations(1, admin_recs)
            print("✅ 管理员推荐生成并保存成功")
        
        # 为普通用户生成新推荐
        user_recs = agent.generate_user_recommendations(2)
        print(f"普通用户新推荐数量: {len(user_recs)}")
        
        if user_recs:
            agent.save_recommendations(2, user_recs)
            print("✅ 普通用户推荐生成并保存成功")
        
        # 2. 测试推荐获取
        print("\n2. 测试推荐获取...")
        agent_service = AgentService()
        
        # 获取管理员推荐
        admin_recommendations = agent_service.get_user_recommendations(1)
        print(f"管理员推荐获取数量: {len(admin_recommendations)}")
        
        management_count = 0
        for rec in admin_recommendations:
            if rec.get('category') == 'management':
                management_count += 1
        
        print(f"其中管理类推荐: {management_count} 条")
        
        # 获取普通用户推荐
        user_recommendations = agent_service.get_user_recommendations(2)
        print(f"普通用户推荐获取数量: {len(user_recommendations)}")
        
        # 3. 验证推荐内容
        print("\n3. 验证推荐内容...")
        
        print("\n管理员推荐详情:")
        for i, rec in enumerate(admin_recommendations[:3], 1):
            print(f"  {i}. {rec.get('type', 'N/A')} - {rec.get('material_name', 'N/A')[:50]}...")
            print(f"     类别: {rec.get('category', 'N/A')}, 评分: {rec.get('score', 'N/A')}")
        
        print("\n普通用户推荐详情:")
        for i, rec in enumerate(user_recommendations[:3], 1):
            print(f"  {i}. {rec.get('type', 'N/A')} - {rec.get('material_name', 'N/A')}")
            print(f"     类别: {rec.get('category', 'N/A')}, 评分: {rec.get('score', 'N/A')}")
        
        # 4. 验证推荐区分
        print("\n4. 验证推荐区分...")
        
        admin_has_management = any(rec.get('category') == 'management' for rec in admin_recommendations)
        user_has_management = any(rec.get('category') == 'management' for rec in user_recommendations)
        
        if admin_has_management and not user_has_management:
            print("✅ 推荐区分正确：管理员有管理类推荐，普通用户没有")
        elif admin_has_management and user_has_management:
            print("⚠️ 推荐区分异常：普通用户也有管理类推荐")
        elif not admin_has_management:
            print("⚠️ 推荐区分异常：管理员没有管理类推荐")
        else:
            print("✅ 推荐区分正确")
        
        print("\n=== 测试完成 ===")
        print("✅ 智能推荐功能测试通过")
        print("✅ 管理员和普通用户推荐区分正常")
        print("✅ 推荐生成、保存、获取流程正常")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_recommendation_flow()
