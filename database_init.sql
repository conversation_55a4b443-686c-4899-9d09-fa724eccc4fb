-- 创建数据库
CREATE DATABASE IF NOT EXISTS goods CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE goods;

-- 科室表
CREATE TABLE departments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '科室名称',
    description TEXT COMMENT '科室描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='科室信息表';

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    role ENUM('admin', 'employee') NOT NULL DEFAULT 'employee' COMMENT '角色：管理员/普通员工',
    department_id INT COMMENT '所属科室ID',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
) COMMENT='用户信息表';

-- 物资表
CREATE TABLE materials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_number VARCHAR(50) UNIQUE COMMENT '资产编号（固定资产专用）',
    name VARCHAR(200) NOT NULL COMMENT '物资名称',
    model VARCHAR(100) COMMENT '型号',
    category ENUM('fixed_asset', 'consumable') NOT NULL COMMENT '类别：固定资产/耗材',
    price DECIMAL(10,2) NOT NULL COMMENT '单价',
    purchase_date DATE NOT NULL COMMENT '购入日期',
    supplier VARCHAR(200) COMMENT '供应商',
    purchase_amount DECIMAL(10,2) COMMENT '采购金额',
    quantity INT DEFAULT 1 COMMENT '数量',
    remaining_quantity INT DEFAULT 1 COMMENT '剩余数量',
    status ENUM('in_use', 'scrapped', 'available') DEFAULT 'available' COMMENT '状态：在用/报废/可用',
    scrap_reason TEXT COMMENT '报废原因',
    scrap_date DATE COMMENT '报废日期',
    description TEXT COMMENT '描述',
    -- 金融相关字段
    depreciation_method ENUM('straight_line', 'declining_balance', 'sum_of_years') DEFAULT 'straight_line' COMMENT '折旧方法：直线法/余额递减法/年数总和法',
    useful_life_years INT DEFAULT 5 COMMENT '使用年限（年）',
    residual_value DECIMAL(10,2) DEFAULT 0 COMMENT '残值',
    accumulated_depreciation DECIMAL(10,2) DEFAULT 0 COMMENT '累计折旧',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    total_usage_hours DECIMAL(10,2) DEFAULT 0 COMMENT '总使用时长（小时）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='物资信息表';

-- 物资分配记录表
CREATE TABLE material_allocations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_id INT NOT NULL COMMENT '物资ID',
    department_id INT NOT NULL COMMENT '分配科室ID',
    user_id INT COMMENT '领取人ID',
    allocated_by INT NOT NULL COMMENT '分配人ID（管理员）',
    quantity INT DEFAULT 1 COMMENT '分配数量',
    allocation_date DATE NOT NULL COMMENT '分配日期',
    return_date DATE COMMENT '归还日期',
    status ENUM('allocated', 'returned', 'consumed') DEFAULT 'allocated' COMMENT '状态：已分配/已归还/已消耗',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (allocated_by) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='物资分配记录表';

-- 物资申请表
CREATE TABLE material_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_id INT NOT NULL COMMENT '申请的物资ID',
    user_id INT NOT NULL COMMENT '申请人ID',
    department_id INT NOT NULL COMMENT '申请科室ID',
    quantity INT DEFAULT 1 COMMENT '申请数量',
    request_date DATE NOT NULL COMMENT '申请日期',
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT '状态：待审核/已批准/已拒绝',
    approved_by INT COMMENT '审批人ID',
    approval_date DATE COMMENT '审批日期',
    reason TEXT COMMENT '申请理由',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
) COMMENT='物资申请表';

-- 企业财务数据表
CREATE TABLE company_financials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    year INT NOT NULL COMMENT '年份',
    month INT NOT NULL COMMENT '月份',
    monthly_operating_cash_flow DECIMAL(15,2) NOT NULL COMMENT '月度经营现金流',
    total_assets DECIMAL(15,2) NOT NULL COMMENT '总资产',
    average_asset_turnover_rate DECIMAL(5,4) DEFAULT 1.0 COMMENT '企业平均资产周转率',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_year_month (year, month)
) COMMENT='企业财务数据表';

-- 耗材采购现金流表
CREATE TABLE consumable_purchases (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_id INT NOT NULL COMMENT '耗材ID',
    purchase_date DATE NOT NULL COMMENT '采购日期',
    quantity INT NOT NULL COMMENT '采购数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_amount DECIMAL(12,2) NOT NULL COMMENT '总金额',
    supplier VARCHAR(200) COMMENT '供应商',
    payment_method ENUM('cash', 'bank_transfer', 'credit') DEFAULT 'bank_transfer' COMMENT '付款方式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE
) COMMENT='耗材采购现金流表';

-- 插入初始数据
INSERT INTO departments (name, description) VALUES
('行政部', '负责行政管理工作'),
('财务部', '负责财务管理工作'),
('技术部', '负责技术开发工作'),
('人事部', '负责人力资源管理'),
('市场部', '负责市场营销工作');

INSERT INTO users (username, password, real_name, role, department_id, email, phone) VALUES
('admin', 'admin123', '系统管理员', 'admin', 1, '<EMAIL>', '***********'),
('zhangsan', 'password123', '张三', 'employee', 2, '<EMAIL>', '***********'),
('lisi', 'password123', '李四', 'employee', 3, '<EMAIL>', '***********'),
('wangwu', 'password123', '王五', 'employee', 4, '<EMAIL>', '***********');

-- 插入企业财务数据示例（2024年数据）
INSERT INTO company_financials (year, month, monthly_operating_cash_flow, total_assets, average_asset_turnover_rate) VALUES
(2024, 1, 5000000.00, 50000000.00, 1.2),
(2024, 2, 4800000.00, 51000000.00, 1.15),
(2024, 3, 5200000.00, 52000000.00, 1.25),
(2024, 4, 4900000.00, 52500000.00, 1.18),
(2024, 5, 5300000.00, 53000000.00, 1.22),
(2024, 6, 5100000.00, 53500000.00, 1.20),
(2024, 7, 5400000.00, 54000000.00, 1.24),
(2024, 8, 5200000.00, 54500000.00, 1.21),
(2024, 9, 5500000.00, 55000000.00, 1.26),
(2024, 10, 5300000.00, 55500000.00, 1.23),
(2024, 11, 5600000.00, 56000000.00, 1.27),
(2024, 12, 5800000.00, 56500000.00, 1.30);

-- 创建索引
CREATE INDEX idx_materials_category ON materials(category);
CREATE INDEX idx_materials_status ON materials(status);
CREATE INDEX idx_materials_purchase_date ON materials(purchase_date);
CREATE INDEX idx_allocations_material_id ON material_allocations(material_id);
CREATE INDEX idx_allocations_department_id ON material_allocations(department_id);
CREATE INDEX idx_allocations_date ON material_allocations(allocation_date);
CREATE INDEX idx_requests_status ON material_requests(status);
CREATE INDEX idx_requests_user_id ON material_requests(user_id);
