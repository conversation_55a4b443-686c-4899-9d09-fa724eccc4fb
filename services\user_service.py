from dao.user_dao import UserDAO
from dao.department_dao import DepartmentDAO
from models.user import User
from datetime import datetime
import hashlib

class UserService:
    def __init__(self):
        self.user_dao = UserDAO()
        self.department_dao = DepartmentDAO()
    
    def get_users_with_filters(self, filters):
        """根据筛选条件获取用户列表"""
        users = self.user_dao.get_all_users()
        
        # 获取科室信息
        departments = {dept.id: dept.name for dept in self.department_dao.get_all_departments()}
        
        # 转换为字典格式并添加科室名称
        user_list = []
        for user in users:
            user_dict = user.to_dict()
            user_dict['department_name'] = departments.get(user.department_id, '-')
            user_list.append(user_dict)
        
        # 应用筛选条件
        if filters.get('keyword'):
            keyword = filters['keyword'].lower()
            user_list = [u for u in user_list if 
                        keyword in u['username'].lower() or 
                        keyword in u['real_name'].lower() or
                        keyword in (u['email'] or '').lower()]
        
        if filters.get('role'):
            user_list = [u for u in user_list if u['role'] == filters['role']]
        
        if filters.get('department_id'):
            user_list = [u for u in user_list if u['department_id'] == filters['department_id']]
        
        if filters.get('status'):
            user_list = [u for u in user_list if u['status'] == filters['status']]
        
        return user_list
    
    def create_user(self, user_data):
        """创建新用户"""
        # 检查用户名是否已存在
        existing_user = self.user_dao.get_user_by_username(user_data['username'])
        if existing_user:
            raise ValueError("用户名已存在")
        
        # 验证科室是否存在
        if user_data.get('department_id'):
            department = self.department_dao.get_department_by_id(user_data['department_id'])
            if not department:
                raise ValueError("指定的科室不存在")
        
        # 创建用户对象
        user = User(
            username=user_data['username'],
            password=self._hash_password(user_data['password']),
            real_name=user_data['real_name'],
            role=user_data['role'],
            department_id=user_data.get('department_id'),
            email=user_data.get('email'),
            phone=user_data.get('phone'),
            status=user_data.get('status', 'active')
        )
        
        return self.user_dao.create_user(user)
    
    def get_user_detail(self, user_id):
        """获取用户详情"""
        user = self.user_dao.get_user_by_id(user_id)
        if not user:
            raise ValueError("用户不存在")
        
        user_dict = user.to_dict()
        
        # 添加科室名称
        if user.department_id:
            department = self.department_dao.get_department_by_id(user.department_id)
            user_dict['department_name'] = department.name if department else '-'
        else:
            user_dict['department_name'] = '-'
        
        return user_dict
    
    def update_user(self, user_id, user_data):
        """更新用户信息"""
        user = self.user_dao.get_user_by_id(user_id)
        if not user:
            raise ValueError("用户不存在")
        
        # 验证科室是否存在
        if user_data.get('department_id'):
            department = self.department_dao.get_department_by_id(user_data['department_id'])
            if not department:
                raise ValueError("指定的科室不存在")
        
        # 更新用户信息
        for key, value in user_data.items():
            if hasattr(user, key) and value is not None:
                setattr(user, key, value)
        
        return self.user_dao.update_user(user)
    
    def delete_user(self, user_id):
        """删除用户"""
        user = self.user_dao.get_user_by_id(user_id)
        if not user:
            raise ValueError("用户不存在")
        
        # 检查用户是否有相关的申请或分配记录
        # 这里可以添加更多的业务逻辑检查
        
        return self.user_dao.delete_user(user_id)
    
    def toggle_user_status(self, user_id):
        """切换用户状态"""
        user = self.user_dao.get_user_by_id(user_id)
        if not user:
            raise ValueError("用户不存在")
        
        # 切换状态
        new_status = 'inactive' if user.status == 'active' else 'active'
        user.status = new_status
        
        return self.user_dao.update_user(user)
    
    def reset_password(self, user_id, new_password):
        """重置用户密码"""
        user = self.user_dao.get_user_by_id(user_id)
        if not user:
            raise ValueError("用户不存在")
        
        hashed_password = self._hash_password(new_password)
        return self.user_dao.update_password(user_id, hashed_password)
    
    def _hash_password(self, password):
        """密码哈希（简单实现，实际项目中应使用更安全的方法）"""
        # 这里使用简单的哈希，实际项目中应该使用bcrypt或其他安全的哈希方法
        return hashlib.md5(password.encode()).hexdigest()
    
    def get_user_statistics(self):
        """获取用户统计信息"""
        users = self.user_dao.get_all_users()
        
        stats = {
            'total_users': len(users),
            'active_users': len([u for u in users if u.status == 'active']),
            'inactive_users': len([u for u in users if u.status == 'inactive']),
            'admin_users': len([u for u in users if u.role == 'admin']),
            'employee_users': len([u for u in users if u.role == 'employee'])
        }
        
        return stats
