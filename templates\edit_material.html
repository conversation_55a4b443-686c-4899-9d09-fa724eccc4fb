{% extends "base.html" %}

{% block title %}编辑物资 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">编辑物资</h1>
        <p>修改物资的基本信息</p>
    </div>
    
    <form method="POST">
        <div class="form-group">
            <label for="name" class="form-label">物资名称 *</label>
            <input type="text" id="name" name="name" class="form-control" value="{{ material.name }}" required>
        </div>
        
        <div class="form-group">
            <label for="model" class="form-label">型号</label>
            <input type="text" id="model" name="model" class="form-control" value="{{ material.model or '' }}">
        </div>
        
        <div class="form-group">
            <label for="category" class="form-label">类别 *</label>
            <select id="category" name="category" class="form-control form-select" required>
                <option value="">请选择类别</option>
                <option value="fixed_asset" {% if material.category == 'fixed_asset' %}selected{% endif %}>固定资产</option>
                <option value="consumable" {% if material.category == 'consumable' %}selected{% endif %}>耗材</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="price" class="form-label">单价 (元) *</label>
            <input type="number" id="price" name="price" class="form-control" step="0.01" min="0" value="{{ material.price }}" required>
        </div>
        
        <div class="form-group">
            <label for="purchase_date" class="form-label">购入日期 *</label>
            <input type="date" id="purchase_date" name="purchase_date" class="form-control" value="{{ material.purchase_date }}" required>
        </div>
        
        <div class="form-group">
            <label for="supplier" class="form-label">供应商</label>
            <input type="text" id="supplier" name="supplier" class="form-control" value="{{ material.supplier or '' }}">
        </div>
        
        <div class="form-group">
            <label for="purchase_amount" class="form-label">采购金额 (元)</label>
            <input type="number" id="purchase_amount" name="purchase_amount" class="form-control" step="0.01" min="0" value="{{ material.purchase_amount or 0 }}">
        </div>
        
        <div class="form-group">
            <label for="quantity" class="form-label">总数量 *</label>
            <input type="number" id="quantity" name="quantity" class="form-control" min="1" value="{{ material.quantity }}" required>
        </div>
        
        <div class="form-group">
            <label for="remaining_quantity" class="form-label">剩余数量 *</label>
            <input type="number" id="remaining_quantity" name="remaining_quantity" class="form-control" min="0" value="{{ material.remaining_quantity }}" required>
        </div>
        
        <div class="form-group">
            <label for="status" class="form-label">状态 *</label>
            <select id="status" name="status" class="form-control form-select" required>
                <option value="available" {% if material.status == 'available' %}selected{% endif %}>可用</option>
                <option value="in_use" {% if material.status == 'in_use' %}selected{% endif %}>在用</option>
                <option value="scrapped" {% if material.status == 'scrapped' %}selected{% endif %}>已报废</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="description" class="form-label">描述</label>
            <textarea id="description" name="description" class="form-control" rows="3">{{ material.description or '' }}</textarea>
        </div>
        
        <div class="d-flex gap-2">
            <button type="submit" class="btn btn-success">保存修改</button>
            <a href="{{ url_for('material.material_detail', material_id=material.id) }}" class="btn btn-secondary">取消</a>
        </div>
    </form>
</div>

<script>
// 自动计算采购金额
document.getElementById('price').addEventListener('input', calculatePurchaseAmount);
document.getElementById('quantity').addEventListener('input', calculatePurchaseAmount);

function calculatePurchaseAmount() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const quantity = parseInt(document.getElementById('quantity').value) || 0;
    const purchaseAmount = price * quantity;
    
    if (purchaseAmount > 0) {
        document.getElementById('purchase_amount').value = purchaseAmount.toFixed(2);
    }
}

// 验证剩余数量不能超过总数量
document.getElementById('remaining_quantity').addEventListener('input', function() {
    const totalQuantity = parseInt(document.getElementById('quantity').value) || 0;
    const remainingQuantity = parseInt(this.value) || 0;
    
    if (remainingQuantity > totalQuantity) {
        alert('剩余数量不能超过总数量');
        this.value = totalQuantity;
    }
});

document.getElementById('quantity').addEventListener('input', function() {
    const totalQuantity = parseInt(this.value) || 0;
    const remainingQuantity = parseInt(document.getElementById('remaining_quantity').value) || 0;
    
    if (remainingQuantity > totalQuantity) {
        document.getElementById('remaining_quantity').value = totalQuantity;
    }
});
</script>
{% endblock %}
