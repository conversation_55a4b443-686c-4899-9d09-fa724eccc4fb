#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AgentService的推荐功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.agent_service import AgentService

def test_agent_service():
    """测试AgentService推荐功能"""
    try:
        agent_service = AgentService()
        
        print("=== 测试管理员推荐 ===")
        admin_recs = agent_service.get_user_recommendations(1)  # 管理员用户ID=1
        print(f"管理员推荐数量: {len(admin_recs)}")
        
        for i, rec in enumerate(admin_recs, 1):
            print(f"\n{i}. 推荐项目:")
            print(f"   物资名称: {rec.get('material_name', 'N/A')}")
            print(f"   类别: {rec.get('category', 'N/A')}")
            print(f"   类型: {rec.get('type', 'N/A')}")
            print(f"   评分: {rec.get('score', 'N/A')}")
            print(f"   原因: {rec.get('reason', 'N/A')[:80]}...")
            print(f"   物资ID: {rec.get('material_id', 'N/A')}")
        
        print("\n=== 测试普通用户推荐 ===")
        user_recs = agent_service.get_user_recommendations(2)  # 普通用户ID=2
        print(f"普通用户推荐数量: {len(user_recs)}")
        
        for i, rec in enumerate(user_recs, 1):
            print(f"\n{i}. 推荐项目:")
            print(f"   物资名称: {rec.get('material_name', 'N/A')}")
            print(f"   类别: {rec.get('category', 'N/A')}")
            print(f"   类型: {rec.get('type', 'N/A')}")
            print(f"   评分: {rec.get('score', 'N/A')}")
            print(f"   原因: {rec.get('reason', 'N/A')[:80]}...")
            print(f"   物资ID: {rec.get('material_id', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_agent_service()
