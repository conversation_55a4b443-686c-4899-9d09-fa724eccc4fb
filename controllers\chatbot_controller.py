from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for
from services.chatbot_service import chatbot_service
import logging

chatbot_bp = Blueprint('chatbot', __name__)
logger = logging.getLogger(__name__)

@chatbot_bp.route('/chatbot')
def chatbot_page():
    """聊天机器人页面"""
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))
    
    return render_template('chatbot.html')

@chatbot_bp.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天请求"""
    try:
        if 'user_id' not in session:
            return jsonify({
                "success": False,
                "error": "请先登录"
            }), 401
        
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({
                "success": False,
                "error": "消息内容不能为空"
            }), 400
        
        message = data['message'].strip()
        if not message:
            return jsonify({
                "success": False,
                "error": "消息内容不能为空"
            }), 400
        
        # 获取对话历史
        conversation_history = data.get('history', [])

        # 获取API密钥（如果提供）
        api_key = data.get('api_key')

        # 创建临时的聊天机器人服务实例（如果提供了API密钥）
        if api_key:
            from services.chatbot_service import ChatbotService
            temp_service = ChatbotService(api_key=api_key)
            result = temp_service.chat(message, conversation_history)
        else:
            # 使用默认服务
            result = chatbot_service.chat(message, conversation_history)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"聊天请求处理异常: {e}")
        return jsonify({
            "success": False,
            "error": "服务器内部错误"
        }), 500

@chatbot_bp.route('/api/chat/quick-replies', methods=['GET'])
def get_quick_replies():
    """获取快速回复选项"""
    try:
        if 'user_id' not in session:
            return jsonify({
                "success": False,
                "error": "请先登录"
            }), 401
        
        quick_replies = chatbot_service.get_quick_replies()
        return jsonify({
            "success": True,
            "quick_replies": quick_replies
        })
        
    except Exception as e:
        logger.error(f"获取快速回复异常: {e}")
        return jsonify({
            "success": False,
            "error": "服务器内部错误"
        }), 500

@chatbot_bp.route('/api/chat/test-api', methods=['POST'])
def test_api():
    """测试DeepSeek API连接"""
    try:
        if 'user_id' not in session:
            return jsonify({
                "success": False,
                "error": "请先登录"
            }), 401

        data = request.get_json()
        api_key = data.get('api_key')

        if not api_key:
            return jsonify({
                "success": False,
                "error": "API密钥不能为空"
            }), 400

        # 创建临时服务实例测试API
        from services.chatbot_service import ChatbotService
        test_service = ChatbotService(api_key=api_key)

        # 发送测试消息
        test_result = test_service.chat("你好", [])

        if test_result.get('success'):
            return jsonify({
                "success": True,
                "message": "API连接测试成功"
            })
        else:
            return jsonify({
                "success": False,
                "error": test_result.get('error', 'API测试失败')
            })

    except Exception as e:
        logger.error(f"API测试失败: {e}")
        return jsonify({
            "success": False,
            "error": f"API测试失败: {str(e)}"
        }), 500

@chatbot_bp.route('/api/chat/system-info', methods=['GET'])
def get_system_info():
    """获取系统信息"""
    try:
        if 'user_id' not in session:
            return jsonify({
                "success": False,
                "error": "请先登录"
            }), 401
        
        system_info = chatbot_service.get_system_info()
        return jsonify({
            "success": True,
            "info": system_info
        })
        
    except Exception as e:
        logger.error(f"获取系统信息异常: {e}")
        return jsonify({
            "success": False,
            "error": "服务器内部错误"
        }), 500
