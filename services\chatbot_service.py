import requests
import json
import logging
from typing import Dict, List, Optional
from config import Config

class ChatbotService:
    """AI聊天机器人服务，使用DeepSeek API"""

    def __init__(self, api_key: str = None):
        self.api_key = api_key or Config.DEEPSEEK_API_KEY
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.model = "deepseek-chat"
        self.logger = logging.getLogger(__name__)
        
        # 系统提示词，定义机器人的角色和能力
        self.system_prompt = """你是物资管理系统的智能助手，名字叫"小智"。你的主要职责是：

1. 帮助用户了解物资管理系统的功能和使用方法
2. 回答关于物资申请、审批、分配等流程的问题
3. 提供系统操作指导和故障排除建议
4. 解答物资管理相关的业务问题
5. 协助用户更好地使用系统功能

请用友好、专业的语气回答用户问题，如果遇到不确定的问题，请诚实地说明并建议用户联系系统管理员。
回答要简洁明了，重点突出，适合在聊天界面中显示。"""

    def chat(self, message: str, conversation_history: List[Dict] = None) -> Dict:
        """
        与AI聊天机器人对话
        
        Args:
            message: 用户输入的消息
            conversation_history: 对话历史记录
            
        Returns:
            包含回复内容和状态的字典
        """
        try:
            # 构建消息列表
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # 添加对话历史（最多保留最近10轮对话）
            if conversation_history:
                messages.extend(conversation_history[-20:])  # 保留最近20条消息（10轮对话）
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": message})
            
            # 准备API请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": messages,
                "max_tokens": 1000,
                "temperature": 0.7,
                "stream": False
            }
            
            # 检查是否使用演示模式（API密钥为默认值或空值时）
            if not self.api_key or self.api_key == "sk-your-deepseek-api-key-here":
                # 演示模式：返回模拟回复
                ai_reply = self._get_demo_reply(message)
                return {
                    "success": True,
                    "reply": ai_reply,
                    "error": None
                }

            # 发送请求到真实API
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                ai_reply = result["choices"][0]["message"]["content"]

                return {
                    "success": True,
                    "reply": ai_reply,
                    "error": None
                }
            else:
                self.logger.error(f"DeepSeek API错误: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "reply": "抱歉，AI服务暂时不可用，请稍后再试。",
                    "error": f"API错误: {response.status_code}"
                }
                
        except requests.exceptions.Timeout:
            self.logger.error("DeepSeek API请求超时")
            return {
                "success": False,
                "reply": "抱歉，响应超时，请稍后再试。",
                "error": "请求超时"
            }
        except requests.exceptions.RequestException as e:
            self.logger.error(f"DeepSeek API请求异常: {e}")
            return {
                "success": False,
                "reply": "抱歉，网络连接异常，请检查网络后重试。",
                "error": f"网络异常: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"聊天机器人服务异常: {e}")
            return {
                "success": False,
                "reply": "抱歉，服务出现异常，请联系系统管理员。",
                "error": f"服务异常: {str(e)}"
            }

    def _get_demo_reply(self, message: str) -> str:
        """演示模式的回复生成"""
        message_lower = message.lower()

        # 物资申请相关问题 - 更精确的匹配
        if any(keyword in message_lower for keyword in ['如何申请', '怎么申请', '申请流程', '申请方法']):
            return """📝 物资申请流程：

1. **登录系统** - 使用您的账号登录物资管理系统
2. **浏览物资** - 在"物资管理"页面查看可申请的物资
3. **提交申请** - 点击需要的物资，填写申请数量和用途
4. **等待审批** - 管理员会审核您的申请
5. **查看结果** - 在"申请状态"中查看审批结果

💡 小贴士：申请时请详细说明使用用途，有助于提高审批通过率！"""

        # 申请状态查询 - 更精确的匹配
        elif any(keyword in message_lower for keyword in ['申请状态', '状态查询', '查询状态', '申请进度', '进度查询']):
            return """🔍 查询申请状态：

您可以通过以下方式查看申请状态：
• 点击导航栏"分配记录"查看所有申请
• 申请状态包括：待审核、已批准、已拒绝、已分配
• 如有疑问，可联系管理员了解详情

📊 状态说明：
- 🟡 待审核：申请已提交，等待管理员处理
- 🟢 已批准：申请通过，等待物资分配
- 🔴 已拒绝：申请未通过，可查看拒绝原因
- 🔵 已分配：物资已分配完成"""

        # 系统功能介绍
        elif any(keyword in message_lower for keyword in ['系统功能', '主要功能', '功能介绍', '系统介绍']):
            return """🎯 系统主要功能：

**用户功能：**
• 📋 物资查询 - 浏览可申请的物资信息
• 📝 申请提交 - 提交物资申请
• 📊 状态跟踪 - 查看申请处理进度

**管理功能：**
• 🏗️ 物资管理 - 添加、修改、删除物资
• ✅ 申请审批 - 审核用户申请
• 📦 分配管理 - 处理物资分配
• 📈 统计报表 - 查看使用统计

**智能功能：**
• 🤖 AI推荐 - 个性化物资推荐
• ⚠️ 库存预警 - 自动库存提醒
• 📊 数据分析 - 智能数据洞察"""

        # 使用帮助
        elif any(keyword in message_lower for keyword in ['使用帮助', '操作指南', '怎么用', '如何使用']):
            return """📖 系统使用帮助：

**新用户指南：**
1. 首次登录后，建议先浏览"物资管理"了解可用物资
2. 查看"仪表板"了解系统概况和个人统计
3. 使用"AI助手"获得实时帮助

**常用操作：**
• 申请物资：物资管理 → 选择物资 → 填写申请
• 查看进度：分配记录 → 查看申请状态
• 查看统计：统计报表 → 各类数据分析

**小贴士：**
• 申请时详细填写用途说明
• 定期查看系统通知
• 遇到问题可随时咨询AI助手"""

        # 联系管理员
        elif any(keyword in message_lower for keyword in ['联系管理员', '管理员联系', '人工客服', '技术支持']):
            return """👨‍💼 联系管理员：

如需人工帮助，您可以：
• 在系统内查看管理员联系方式
• 通过系统通知功能发送消息
• 拨打技术支持热线

⏰ 工作时间：周一至周五 9:00-18:00
📧 邮箱支持：<EMAIL>
📞 电话支持：400-123-4567

我会尽力帮助您解决问题，如果我无法解答，建议您联系管理员获得更专业的帮助。"""

        # 问候语
        elif any(keyword in message_lower for keyword in ['你好', '您好', 'hello', 'hi']):
            return """👋 您好！我是物资管理系统的智能助手小智！

很高兴为您服务！我可以帮助您：
• 🔍 了解物资申请流程
• 📊 查询申请状态
• 💡 解答系统使用问题
• 📞 提供技术支持信息

请告诉我您需要什么帮助，或者点击下方的快速回复按钮开始对话！"""

        # 物资相关问题
        elif any(keyword in message_lower for keyword in ['物资', '材料', '设备', '用品']):
            return """📦 物资管理相关信息：

**可申请物资类型：**
• 办公用品：文具、纸张、打印耗材等
• 电子设备：电脑、打印机、投影仪等
• 办公家具：桌椅、文件柜、书架等
• 其他用品：清洁用品、安全用品等

**申请建议：**
• 提前规划，避免紧急申请
• 详细说明使用用途和数量
• 关注库存状态，选择合适时机申请

需要了解具体申请流程吗？"""

        # 默认回复
        else:
            # 根据关键词给出更智能的回复
            if any(keyword in message_lower for keyword in ['问题', '错误', '故障', '异常']):
                return f"""🔧 问题排查帮助：

您提到："{message}"

常见问题解决方案：
• **登录问题**：检查用户名密码，联系管理员重置
• **申请问题**：确认物资库存，检查申请权限
• **页面问题**：刷新浏览器，清除缓存
• **功能问题**：查看使用帮助，联系技术支持

需要更详细的帮助吗？请描述具体遇到的问题。"""

            elif any(keyword in message_lower for keyword in ['谢谢', '感谢', 'thanks', '再见', 'bye']):
                return """😊 不客气！很高兴能帮助您！

如果还有其他问题，随时可以找我：
• 点击快速回复按钮
• 直接输入您的问题
• 查看系统帮助文档

祝您使用愉快！👋"""

            else:
                return f"""🤖 智能助手小智为您服务！

您询问："{message}"

我可能没有完全理解您的问题。请尝试：
• 使用更具体的关键词
• 点击下方快速回复按钮
• 或者这样问我：
  - "如何申请物资？"
  - "怎么查看申请状态？"
  - "系统有什么功能？"

我会尽力为您提供准确的帮助！"""

    def get_quick_replies(self) -> List[Dict]:
        """获取快速回复选项"""
        return [
            {"text": "如何申请物资？", "value": "请告诉我如何申请物资"},
            {"text": "申请状态查询", "value": "我想查询申请状态"},
            {"text": "系统使用帮助", "value": "请介绍系统的主要功能"},
            {"text": "联系管理员", "value": "如何联系系统管理员"},
        ]

    def get_system_info(self) -> str:
        """获取系统信息介绍"""
        return """欢迎使用物资管理系统！我是您的智能助手小智。

🔧 主要功能：
• 物资查询和申请
• 申请状态跟踪
• 管理员审批和分配
• 智能推荐和预警

❓ 常见问题：
• 如何申请物资？
• 申请被拒绝怎么办？
• 如何查看申请进度？

有任何问题都可以问我哦！"""

# 创建全局聊天机器人实例
chatbot_service = ChatbotService()
