# 金融企业物资管理系统编码案例说明

## 1. 导言

### 1.1 目的
该文档的目的是描述金融企业物资管理系统项目的编码规范和对代码的说明，其主要内容包括：
- 编码规范
- 命名规范
- 注释规范
- 语句规范
- 声明规范
- 目录设置
- 代码说明

本文档的预期读者是：
- 开发人员
- 项目管理人员
- 质量保证人员

### 1.2 范围
该文档定义了本项目的代码编写规范以及部分代码描述和所有代码的说明。

### 1.3 编写说明
Flask：基于Python的轻量级Web应用框架。
MVC：Model-View-Controller（模型—视图—控制器）的缩写，表示一个三层的结构体系。
DAO：Data Access Object（数据访问对象）的缩写。
Agent：智能代理系统的缩写。

### 1.4 术语定义
Class：Python程序中的一个程序单位，可以生成很多的实例。
Module：由很多的类和函数组成的工作模块。
Blueprint：Flask框架中的蓝图模块，用于组织路由和视图。

### 1.5 引用标准
[1]《企业文档格式标准》V1.1，金融企业物资管理有限公司
[2]《Python语言编写规范》V1.1，金融企业物资管理有限公司软件工程过程化组织

### 1.6 参考资料
[1]《Flask Web开发实战》李辉 人民邮电出版社
[2]《Python编程：从入门到实践》Eric Matthes 人民邮电出版社

### 1.7 版本更新信息
本文档版更新记录如表1-1所示。

| 修改编号 | 修改日期 | 修改后版本 | 修改位置 | 修改内容概述 |
|----------|----------|------------|----------|--------------|
| 000 | 2025.01.07 | 1.0 | 全部 | 初始发布版本 |
| 001 | 2025.01.08 | 1.1 | 第6章 | 修改代码规范 |

## 2. 编码格式规范

### 2.1 缩进排版
4个空格作为缩进排版的一个单位，严格遵循Python PEP 8编码规范。

### 2.2 行长度
尽量避免一行的长度超过88个字符，用于文档中的例子应该使用更短的行长，长度一般不超过80个字符。

### 2.3 断行规则
当一个表达式无法容纳在一行内时，可以依据如下一般规则断开：
- 在一个逗号后面断开；
- 在一个操作符前面断开；
- 宁可选择较高级别的断开，而非较低级别的断开；
- 新的一行应该与上一行同一级别表达式的开始处对齐；
- 如果以上规则导致代码混乱或者使代码都堆挤在右边，那就代之以缩进8个空格。

以下是两个断开算术表达式的例子，前者属于更高级别的断开：
```python
# 推荐
total_score = (personal_score + department_score + 
               popularity_score + time_factor)

# 避免
total_score = (personal_score + department_score + popularity_score + 
               time_factor)
```

以下是两个缩进方法说明的例子：
```python
# 规范的缩进
def calculate_recommendation_score(user_id, material_id, 
                                 personal_weight, dept_weight):
    ...

# 以8个空格来缩进，以避免非常纵深的缩进
def calculate_very_long_recommendation_score_with_multiple_parameters(
        user_id, material_id, personal_weight, dept_weight, 
        popularity_weight, time_factor):
    ...
```

### 2.4 空行
空行将逻辑相关的代码段分隔开，以提高可读性。下列情况应该总是使用两个空行：
- 一个源文件的两个类之间；
- 类声明和函数声明之间。

下列情况应该总是使用一个空行：
- 两个方法之间；
- 方法内的局部变量和方法的第一条语句之间；
- 块注释或单行注释之前；
- 一个方法内的两个逻辑段之间，用以提高可读性。

## 3. 命名规范

### 3.1 模块（Modules）
模块名应该是简短的、小写的名字，如果下划线可以改善可读性可以加入。如：
```
material_dao
user_service
smart_recommendation_agent
```

### 3.2 类（Classes）
类名使用驼峰命名法（CapWords），每个单词的首字母大写。尽量使你的类名简洁而富于描述。
```python
class MaterialDAO:
class SmartRecommendationAgent:
class UserService:
```

### 3.3 函数和方法（Functions and Methods）
函数名应该为小写，必要时可用下划线分隔单词以增加可读性。
```python
def get_user_recommendations():
def calculate_score():
def generate_personal_recommendations():
```

### 3.4 变量（Variables）
变量名遵循函数命名规则：小写，必要时用下划线分隔。变量名应简短且富于描述。
```python
user_id = 1
material_name = "办公用品"
recommendation_score = 0.85
```

### 3.5 常量（Constants）
常量名全部大写，单词间用下划线分隔。
```python
MAX_RECOMMENDATIONS = 10
DEFAULT_SCORE_WEIGHT = 0.5
DATABASE_CONFIG_FILE = "config.py"
```

## 4. 声明规范

### 4.1 导入声明
导入应该分组进行，组之间用空行分隔，按以下顺序：
1. 标准库导入
2. 相关第三方库导入
3. 本地应用/库导入

```python
# 标准库导入
import os
import sys
from datetime import datetime

# 第三方库导入
from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy

# 本地导入
from dao.material_dao import MaterialDAO
from services.user_service import UserService
```

### 4.2 变量初始化
尽量在声明局部变量的同时初始化。
```python
def calculate_score():
    base_score = 0.0  # 基础评分
    weight_factor = 1.0  # 权重因子
    
    if condition:
        adjustment_score = 0.1  # 调整评分
```

### 4.3 类和函数的声明
当编写类和函数时，应该遵守以下格式规则：
```python
class MaterialService:
    """物资服务类"""
    
    def __init__(self):
        self.dao = MaterialDAO()
    
    def get_material_by_id(self, material_id):
        """根据ID获取物资信息"""
        return self.dao.find_by_id(material_id)
```

## 5. 语句规范

### 5.1 简单语句
每行至多包含一条语句，例如：
```python
user_id += 1  # 推荐
score += 0.1  # 推荐

user_id += 1; score += 0.1  # 避免
```

### 5.2 复合语句
复合语句遵循以下原则：
```python
# 推荐的if语句格式
if condition:
    do_something()
elif other_condition:
    do_something_else()
else:
    do_default()

# 推荐的for循环格式
for item in items:
    process_item(item)

# 推荐的try-except格式
try:
    risky_operation()
except SpecificException as e:
    handle_exception(e)
finally:
    cleanup()
```

## 6. 注释规范

### 6.1 注释的方法

#### 1）文档字符串（Docstrings）
所有的公共模块、函数、类以及方法都应该有文档字符串。
```python
def generate_user_recommendations(user_id):
    """
    为指定用户生成个性化推荐

    Args:
        user_id (int): 用户ID

    Returns:
        list: 推荐结果列表，每个元素包含material_id, score, reason

    Raises:
        ValueError: 当user_id无效时抛出
    """
    pass
```

#### 2）行内注释
行内注释应该谨慎使用，与代码用至少两个空格分隔。
```python
user_score = calculate_base_score()  # 计算基础评分
```

#### 3）块注释
块注释通常用于解释复杂的算法或业务逻辑。
```python
# 智能推荐算法实现
# 1. 获取用户历史行为数据
# 2. 分析部门趋势
# 3. 计算综合评分
# 4. 排序并返回前N个推荐
```

### 6.2 开头注释
所有的源文件都应该在开头有一个注释，其中列出模块名、版本信息、日期、作者以及版权声明：

```python
"""
@System: 金融企业物资管理系统
@Version: 1.0
@Copyright: 2025 by 金融企业物资管理有限公司
@Module: smart_recommendation_agent
@Summary: 智能推荐代理模块
@Create: 2025.01.07 张三
@Update: 2025.01.08 李四
"""
```

### 6.3 类和方法的注释
类和方法文档注释应包含完整的功能描述、参数说明和返回值说明：

```python
class SmartRecommendationAgent:
    """
    智能推荐代理类

    该类负责为用户生成个性化的物资推荐，基于用户历史行为、
    部门趋势和全局热门物资进行综合评分。

    Attributes:
        material_dao (MaterialDAO): 物资数据访问对象
        notification_service (NotificationService): 通知服务
    """

    def generate_user_recommendations(self, user_id):
        """
        为用户生成个性化推荐

        基于用户历史申请记录、部门同事的申请趋势以及全局热门物资，
        使用加权评分算法生成个性化推荐列表。

        Args:
            user_id (int): 用户ID

        Returns:
            list: 推荐结果列表，每个元素包含：
                - material_id (int): 物资ID
                - material_name (str): 物资名称
                - score (float): 推荐评分 (0-1)
                - reason (str): 推荐理由
                - type (str): 推荐类型 (personal/department/popular)

        Raises:
            ValueError: 当user_id无效时抛出
            DatabaseError: 当数据库操作失败时抛出
        """
        pass
```

## 7. 代码范例

### 7.1 智能推荐代理完整实现

```python
"""
@System: 金融企业物资管理系统
@Version: 1.0
@Copyright: 2025 by 金融企业物资管理有限公司
@Module: smart_recommendation_agent
@Summary: 智能推荐代理模块
@Create: 2025.01.07 张三
@Update: 2025.01.08 李四
"""

# 标准库导入
from datetime import datetime, timedelta
import logging
import random

# 第三方库导入
from flask import current_app

# 本地导入
from agents.base_agent import BaseAgent
from dao.material_dao import MaterialDAO
from services.notification_service import NotificationService


class SmartRecommendationAgent(BaseAgent):
    """
    智能推荐代理类

    该类负责为用户生成个性化的物资推荐，基于用户历史行为、
    部门趋势和全局热门物资进行综合评分。
    """

    # 常量定义
    MAX_RECOMMENDATIONS = 5
    PERSONAL_WEIGHT = 0.4
    DEPARTMENT_WEIGHT = 0.3
    POPULAR_WEIGHT = 0.2
    TIME_WEIGHT = 0.1

    def __init__(self):
        """初始化智能推荐代理"""
        super().__init__("SmartRecommendation")
        self.material_dao = MaterialDAO()
        self.notification_service = NotificationService()

        # 配置日志
        self.logger = logging.getLogger(__name__)

    def execute(self):
        """
        执行智能推荐任务

        为所有活跃用户生成推荐，并返回执行结果统计。

        Returns:
            dict: 执行结果，包含处理用户数和生成推荐数
        """
        try:
            # 获取活跃用户列表
            active_users = self._get_active_users()
            total_recommendations = 0

            # 为每个活跃用户生成推荐
            for user in active_users:
                recommendations = self.generate_user_recommendations(user['id'])
                total_recommendations += len(recommendations)

                self.logger.info(
                    f"为用户 {user['username']} 生成了 {len(recommendations)} 条推荐"
                )

            result = {
                'users_processed': len(active_users),
                'recommendations_generated': total_recommendations,
                'execution_time': datetime.now().isoformat()
            }

            self.logger.info(f"推荐任务执行完成: {result}")
            return result

        except Exception as e:
            self.logger.error(f"推荐任务执行失败: {str(e)}")
            raise

    def generate_user_recommendations(self, user_id):
        """
        为用户生成个性化推荐

        Args:
            user_id (int): 用户ID

        Returns:
            list: 推荐结果列表

        Raises:
            ValueError: 当user_id无效时抛出
        """
        if not user_id or user_id <= 0:
            raise ValueError("用户ID无效")

        recommendations = []

        # 1. 获取用户信息和历史行为
        user_info = self._get_user_info(user_id)
        if not user_info:
            self.logger.warning(f"用户 {user_id} 信息不存在")
            return recommendations

        user_history = self._get_user_history(user_id)

        # 2. 生成不同类型的推荐
        personal_recs = self._generate_personal_recommendations(user_history)
        dept_recs = self._generate_department_recommendations(
            user_info['department_id']
        )
        popular_recs = self._generate_popular_recommendations()

        # 3. 合并所有推荐
        all_recommendations = personal_recs + dept_recs + popular_recs

        # 4. 计算推荐评分并排序
        scored_recs = self._calculate_recommendation_scores(
            all_recommendations, user_info
        )

        # 5. 取前N个推荐并保存
        top_recommendations = scored_recs[:self.MAX_RECOMMENDATIONS]
        self._save_recommendations(user_id, top_recommendations)

        return top_recommendations

    def _get_user_info(self, user_id):
        """获取用户基本信息"""
        sql = """
        SELECT id, username, role, department_id, real_name
        FROM users
        WHERE id = %s AND is_active = 1
        """
        result = self.material_dao.db.execute_query(sql, (user_id,))
        return result[0] if result else None

    def _get_user_history(self, user_id):
        """获取用户历史申请记录"""
        sql = """
        SELECT
            mr.material_id,
            m.name as material_name,
            COUNT(*) as request_count,
            MAX(mr.created_at) as last_request,
            AVG(mr.quantity) as avg_quantity
        FROM material_requests mr
        JOIN materials m ON mr.material_id = m.id
        WHERE mr.user_id = %s
        AND mr.created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        AND mr.status IN ('approved', 'completed')
        GROUP BY mr.material_id, m.name
        ORDER BY request_count DESC, last_request DESC
        LIMIT 10
        """
        return self.material_dao.db.execute_query(sql, (user_id,))

    def _generate_personal_recommendations(self, user_history):
        """基于个人历史生成推荐"""
        recommendations = []

        for item in user_history[:3]:  # 取前3个最常申请的物资
            # 计算频率评分
            frequency_score = min(item['request_count'] / 10.0, 1.0)

            # 计算时间衰减
            days_since_last = (datetime.now() - item['last_request']).days
            time_decay = max(0.1, 1.0 - (days_since_last / 180.0))

            recommendations.append({
                'material_id': item['material_id'],
                'material_name': item['material_name'],
                'type': 'personal',
                'reason': f'您在过去6个月内申请过{item["request_count"]}次',
                'frequency_score': frequency_score,
                'time_decay': time_decay,
                'base_score': 0.8
            })

        return recommendations

    def _calculate_recommendation_scores(self, recommendations, user_info):
        """
        计算推荐评分

        使用加权评分算法，综合考虑个人偏好、部门相关性、
        全局热门度和时间因子。
        """
        for rec in recommendations:
            score = rec.get('base_score', 0.5)

            # 个人偏好权重
            if rec['type'] == 'personal':
                personal_factor = (
                    rec.get('frequency_score', 0) * 0.7 +
                    rec.get('time_decay', 0) * 0.3
                )
                score += self.PERSONAL_WEIGHT * personal_factor

            # 部门相关性权重
            elif rec['type'] == 'department':
                dept_factor = rec.get('relevance_score', 0)
                score += self.DEPARTMENT_WEIGHT * dept_factor

            # 全局热门权重
            elif rec['type'] == 'popular':
                popular_factor = rec.get('popularity_score', 0)
                score += self.POPULAR_WEIGHT * popular_factor

            # 时间随机因子
            time_factor = random.uniform(0.05, 0.15)
            score += self.TIME_WEIGHT * time_factor

            # 角色权重调整
            if user_info.get('role') == 'manager':
                score += 0.05  # 管理员获得额外权重

            # 限制评分范围
            rec['score'] = max(0.0, min(score, 1.0))

        # 按评分降序排序
        return sorted(recommendations, key=lambda x: x['score'], reverse=True)
```

### 7.2 Flask路由控制器范例

```python
"""
@System: 金融企业物资管理系统
@Version: 1.0
@Copyright: 2025 by 金融企业物资管理有限公司
@Module: agent_controller
@Summary: 智能代理控制器
@Create: 2025.01.07 王五
@Update:
"""

from flask import Blueprint, render_template, request, jsonify, session
from flask import current_app

from services.agent_service import AgentService
from agents.smart_recommendation_agent import SmartRecommendationAgent
from utils.decorators import login_required, admin_required
from utils.response import success_response, error_response


# 创建蓝图
agent_bp = Blueprint('agent', __name__, url_prefix='/agent')


@agent_bp.route('/recommendations')
@login_required
def recommendations():
    """
    智能推荐页面

    显示当前用户的个性化推荐列表，支持分页和筛选。

    Returns:
        str: 渲染后的HTML页面
    """
    try:
        user_id = session.get('user_id')
        page = request.args.get('page', 1, type=int)

        # 获取用户推荐
        agent_service = AgentService()
        recommendations = agent_service.get_user_recommendations(
            user_id, page=page, per_page=10
        )

        # 获取推荐统计信息
        stats = agent_service.get_recommendation_stats(user_id)

        return render_template(
            'agent/recommendations.html',
            recommendations=recommendations,
            stats=stats
        )

    except Exception as e:
        current_app.logger.error(f"获取推荐页面失败: {str(e)}")
        return render_template('error.html', message="页面加载失败")


@agent_bp.route('/api/generate-recommendations', methods=['POST'])
@login_required
def generate_recommendations():
    """
    生成用户推荐API

    为当前用户生成新的个性化推荐。

    Returns:
        dict: JSON响应，包含成功状态和推荐数量
    """
    try:
        user_id = session.get('user_id')

        # 验证用户权限
        if not user_id:
            return error_response("用户未登录", 401)

        # 实例化智能推荐代理
        recommendation_agent = SmartRecommendationAgent()

        # 生成推荐
        recommendations = recommendation_agent.generate_user_recommendations(user_id)

        # 记录操作日志
        current_app.logger.info(
            f"用户 {user_id} 生成了 {len(recommendations)} 条推荐"
        )

        return success_response(
            message=f'成功生成 {len(recommendations)} 条推荐',
            data={'count': len(recommendations)}
        )

    except ValueError as e:
        return error_response(f"参数错误: {str(e)}", 400)
    except Exception as e:
        current_app.logger.error(f"生成推荐失败: {str(e)}")
        return error_response("生成推荐失败，请稍后重试", 500)


@agent_bp.route('/api/run/<agent_name>', methods=['POST'])
@admin_required
def run_agent(agent_name):
    """
    手动执行代理API

    管理员可以手动触发指定代理的执行。

    Args:
        agent_name (str): 代理名称

    Returns:
        dict: JSON响应，包含执行结果
    """
    try:
        # 验证代理名称
        valid_agents = {
            'SmartRecommendation': SmartRecommendationAgent,
            # 可以添加其他代理类型
        }

        if agent_name not in valid_agents:
            return error_response("未知的代理类型", 400)

        # 实例化并执行代理
        agent_class = valid_agents[agent_name]
        agent = agent_class()
        result = agent.run()

        # 记录执行日志
        agent_service = AgentService()
        agent_service.log_agent_execution(agent_name, result, 'manual')

        current_app.logger.info(f"管理员手动执行代理 {agent_name}: {result}")

        return success_response(
            message=f'{agent_name} 代理执行成功',
            data=result
        )

    except Exception as e:
        current_app.logger.error(f"代理执行失败: {str(e)}")
        return error_response(f"代理执行失败: {str(e)}", 500)
```

### 7.3 HTML模板范例

```html
<!--
@System: 金融企业物资管理系统
@Version: 1.0
@Copyright: 2025 by 金融企业物资管理有限公司
@Template: recommendations.html
@Summary: 智能推荐页面模板
@Create: 2025.01.07 张三
@Update:
-->

{% extends "base.html" %}

{% block title %}智能推荐{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- 页面标题 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-magic"></i> 智能推荐
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary"
                                onclick="generateRecommendations()">
                            <i class="fas fa-sync-alt"></i> 生成推荐
                        </button>
                    </div>
                </div>

                <!-- 推荐内容区域 -->
                <div class="card-body">
                    {% if recommendations and recommendations.items %}
                        <div class="row">
                            {% for rec in recommendations.items %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card recommendation-card">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            {{ rec.material_name }}
                                        </h5>
                                        <div class="recommendation-score">
                                            <span class="badge badge-primary">
                                                评分: {{ "%.2f"|format(rec.score) }}
                                            </span>
                                            <span class="badge badge-info">
                                                {{ rec.type|title }}
                                            </span>
                                        </div>
                                        <p class="card-text mt-2">
                                            <small class="text-muted">
                                                {{ rec.reason }}
                                            </small>
                                        </p>
                                        <div class="card-actions mt-3">
                                            <button class="btn btn-sm btn-success"
                                                    onclick="applyMaterial({{ rec.material_id }})">
                                                <i class="fas fa-plus"></i> 申请物资
                                            </button>
                                            <button class="btn btn-sm btn-outline-info"
                                                    onclick="viewMaterialDetail({{ rec.material_id }})">
                                                <i class="fas fa-info-circle"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- 分页导航 -->
                        {% if recommendations.pages > 1 %}
                        <nav aria-label="推荐分页">
                            <ul class="pagination justify-content-center">
                                {% if recommendations.has_prev %}
                                <li class="page-item">
                                    <a class="page-link"
                                       href="{{ url_for('agent.recommendations', page=recommendations.prev_num) }}">
                                        上一页
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in recommendations.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != recommendations.page %}
                                        <li class="page-item">
                                            <a class="page-link"
                                               href="{{ url_for('agent.recommendations', page=page_num) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if recommendations.has_next %}
                                <li class="page-item">
                                    <a class="page-link"
                                       href="{{ url_for('agent.recommendations', page=recommendations.next_num) }}">
                                        下一页
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                    {% else %}
                        <!-- 空状态 -->
                        <div class="empty-state text-center py-5">
                            <i class="fas fa-lightbulb fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">暂无推荐内容</h4>
                            <p class="text-muted">
                                点击"生成推荐"按钮获取个性化推荐，
                                或者先申请一些物资来建立您的使用偏好。
                            </p>
                            <button class="btn btn-primary" onclick="generateRecommendations()">
                                <i class="fas fa-magic"></i> 立即生成推荐
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
/**
 * 生成推荐
 */
function generateRecommendations() {
    // 显示加载状态
    const button = event.target;
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    button.disabled = true;

    // 发送AJAX请求
    $.ajax({
        url: '{{ url_for("agent.generate_recommendations") }}',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // 显示成功消息
                showNotification('success', response.message);

                // 延迟刷新页面以显示新推荐
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                showNotification('error', response.message || '生成推荐失败');
            }
        },
        error: function(xhr, status, error) {
            let message = '网络错误，请稍后重试';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            showNotification('error', message);
        },
        complete: function() {
            // 恢复按钮状态
            button.innerHTML = originalHtml;
            button.disabled = false;
        }
    });
}

/**
 * 申请物资
 */
function applyMaterial(materialId) {
    if (confirm('确定要申请这个物资吗？')) {
        // 跳转到物资申请页面
        window.location.href = `/material/apply/${materialId}`;
    }
}

/**
 * 查看物资详情
 */
function viewMaterialDetail(materialId) {
    // 在新窗口打开物资详情页面
    window.open(`/material/detail/${materialId}`, '_blank');
}

/**
 * 显示通知消息
 */
function showNotification(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    const notification = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon}"></i> ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    // 在页面顶部显示通知
    $('.container-fluid').prepend(notification);

    // 3秒后自动隐藏
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
```

## 8. 目录规范

开发环境使用PyCharm或VSCode，开发之后的代码需要部署到Flask服务器环境上。开发目录结构如图8-1所示：

```
金融企业物资管理系统/
├── app.py                      # Flask应用入口文件
├── config.py                   # 配置文件
├── requirements.txt            # Python依赖包列表
├── README.md                   # 项目说明文档
├── .env                        # 环境变量配置
├── .gitignore                  # Git忽略文件配置
│
├── agents/                     # 智能代理模块
│   ├── __init__.py
│   ├── base_agent.py          # 代理基类
│   ├── smart_recommendation_agent.py  # 智能推荐代理
│   ├── inventory_alert_agent.py       # 库存预警代理
│   └── data_analysis_agent.py         # 数据分析代理
│
├── controllers/                # 控制器层
│   ├── __init__.py
│   ├── auth_controller.py     # 认证控制器
│   ├── material_controller.py # 物资管理控制器
│   ├── agent_controller.py    # 代理控制器
│   └── report_controller.py   # 报表控制器
│
├── dao/                        # 数据访问层
│   ├── __init__.py
│   ├── database.py            # 数据库连接类
│   ├── material_dao.py        # 物资数据访问对象
│   ├── user_dao.py            # 用户数据访问对象
│   └── request_dao.py         # 申请数据访问对象
│
├── services/                   # 服务层
│   ├── __init__.py
│   ├── auth_service.py        # 认证服务
│   ├── material_service.py    # 物资服务
│   ├── agent_service.py       # 代理服务
│   └── notification_service.py # 通知服务
│
├── models/                     # 数据模型
│   ├── __init__.py
│   ├── user.py                # 用户模型
│   ├── material.py            # 物资模型
│   └── request.py             # 申请模型
│
├── templates/                  # HTML模板
│   ├── base.html              # 基础模板
│   ├── auth/                  # 认证相关模板
│   │   ├── login.html
│   │   └── register.html
│   ├── material/              # 物资管理模板
│   │   ├── list.html
│   │   ├── detail.html
│   │   └── apply.html
│   ├── agent/                 # 智能代理模板
│   │   ├── dashboard.html
│   │   ├── recommendations.html
│   │   └── config.html
│   └── report/                # 报表模板
│       ├── statistics.html
│       └── export.html
│
├── static/                     # 静态资源
│   ├── css/                   # 样式文件
│   │   ├── bootstrap.min.css
│   │   ├── adminlte.min.css
│   │   └── custom.css
│   ├── js/                    # JavaScript文件
│   │   ├── jquery.min.js
│   │   ├── bootstrap.min.js
│   │   ├── adminlte.min.js
│   │   └── custom.js
│   ├── images/                # 图片资源
│   └── fonts/                 # 字体文件
│
├── utils/                      # 工具类
│   ├── __init__.py
│   ├── decorators.py          # 装饰器
│   ├── response.py            # 响应工具
│   ├── validators.py          # 验证器
│   └── helpers.py             # 辅助函数
│
├── tests/                      # 测试文件
│   ├── __init__.py
│   ├── test_agents.py         # 代理测试
│   ├── test_controllers.py    # 控制器测试
│   ├── test_services.py       # 服务测试
│   └── test_dao.py            # 数据访问测试
│
├── migrations/                 # 数据库迁移文件
│   ├── 001_create_users.sql
│   ├── 002_create_materials.sql
│   ├── 003_create_requests.sql
│   └── 004_create_recommendations.sql
│
└── docs/                       # 文档目录
    ├── 详细设计.md
    ├── 编码案例说明.md
    ├── API文档.md
    └── 部署指南.md
```

编码过程应该按照详细设计的规划进行，在伪代码的基础上，按照编码标准和规范进行模块编码。开发环境使用PyCharm或VSCode，首先开发人员在开发过程中按照开发的目录将相应的文件存放在指定的目录下，进行调试，如果调试完成，代码评审通过后，放入版本控制系统（Git），再从版本控制系统将代码部署到Flask运行环境中。

### 8.1 部署流程

1. **开发阶段**：开发人员在本地环境按照目录规范编写代码
2. **测试阶段**：运行单元测试和集成测试，确保代码质量
3. **代码评审**：通过代码评审流程，确保符合编码规范
4. **版本控制**：提交代码到Git仓库，打标签发布版本
5. **部署上线**：将代码部署到生产环境的Flask服务器

### 8.2 配置管理

系统采用分环境配置管理：
- **开发环境**：config/development.py
- **测试环境**：config/testing.py
- **生产环境**：config/production.py

通过环境变量FLASK_ENV来切换不同的配置环境，确保各环境的配置隔离和安全性。
```
