{% extends "base.html" %}

{% block title %}系统通知{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1>系统通知</h1>
                <p class="text-muted">查看系统发送的各类通知消息</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell"></i> 通知列表
                        <span class="badge badge-primary ml-2">{{ unread_count }} 未读</span>
                        <button class="btn btn-sm btn-outline-secondary float-right" onclick="location.reload()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if notifications %}
                        <!-- 未读通知 -->
                        {% set unread_notifications = notifications|selectattr('is_read', 'equalto', false)|list %}
                        {% if unread_notifications %}
                        <div class="notification-section">
                            <div class="section-header">
                                <h6 class="text-primary mb-0">
                                    <i class="fas fa-bell"></i> 未读通知 ({{ unread_notifications|length }})
                                </h6>
                            </div>
                            {% for notification in unread_notifications %}
                            <div class="notification-item unread-notification">
                                <div class="notification-content">
                                    <div class="notification-header">
                                        <div class="notification-title">
                                            <i class="fas fa-circle text-primary unread-indicator"></i>
                                            <strong>{{ notification.title }}</strong>
                                            <span class="badge badge-{{ 'danger' if notification.type == 'admin' else 'info' }} ml-2">
                                                {{ '管理员' if notification.type == 'admin' else '系统' }}
                                            </span>
                                        </div>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-success mark-read-btn" onclick="markAsRead({{ notification.id }})">
                                                <i class="fas fa-check"></i> 标记已读
                                            </button>
                                        </div>
                                    </div>
                                    <div class="notification-message">{{ notification.message }}</div>
                                    <div class="notification-time">
                                        <i class="fas fa-clock"></i> {{ notification.created_at.strftime('%Y-%m-%d %H:%M') if notification.created_at else '' }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- 已读通知 -->
                        {% set read_notifications = notifications|selectattr('is_read', 'equalto', true)|list %}
                        {% if read_notifications %}
                        <div class="notification-section read-section">
                            <div class="section-header">
                                <h6 class="text-muted mb-0">
                                    <i class="fas fa-check-circle"></i> 已读通知 ({{ read_notifications|length }})
                                </h6>
                            </div>
                            {% for notification in read_notifications %}
                            <div class="notification-item read-notification">
                                <div class="notification-content">
                                    <div class="notification-header">
                                        <div class="notification-title">
                                            <span class="text-muted">{{ notification.title }}</span>
                                            <span class="badge badge-secondary ml-2">
                                                {{ '管理员' if notification.type == 'admin' else '系统' }}
                                            </span>
                                        </div>
                                        <div class="notification-actions">
                                            <span class="read-status">
                                                <i class="fas fa-check-circle text-success"></i> 已读
                                            </span>
                                        </div>
                                    </div>
                                    <div class="notification-message text-muted">{{ notification.message }}</div>
                                    <div class="notification-time text-muted">
                                        <i class="fas fa-clock"></i> {{ notification.created_at.strftime('%Y-%m-%d %H:%M') if notification.created_at else '' }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无通知</h5>
                            <p class="text-muted">系统会在这里显示重要通知和提醒</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>



<script>
function markAsRead(notificationId) {
    // 使用原生fetch API替代jQuery
    fetch(`/agent/api/notifications/mark_read/${notificationId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('操作失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('网络错误，请稍后重试');
    });
}

// 定期检查未读通知数量
setInterval(function() {
    fetch('/agent/api/notifications/unread_count')
        .then(response => response.json())
        .then(data => {
            if (data.count !== undefined) {
                const badgeElement = document.querySelector('.badge-primary');
                if (badgeElement) {
                    const currentCount = parseInt(badgeElement.textContent);
                    if (data.count !== currentCount) {
                        location.reload(); // 有新通知时刷新页面
                    }
                }
            }
        })
        .catch(error => {
            console.error('检查通知数量失败:', error);
        });
}, 30000); // 每30秒检查一次
</script>

<style>
/* 通知区域样式 */
.notification-section {
    border-bottom: 1px solid #e9ecef;
}

.notification-section:last-child {
    border-bottom: none;
}

.section-header {
    background: #f5f6f7;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.read-section .section-header {
    background: #f0f1f2;
}

/* 通知项样式 */
.notification-item {
    padding: 0;
    border: none;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

.notification-content {
    padding: 20px;
}

.unread-notification {
    background: rgba(70, 130, 180, 0.08);
    border-left: 4px solid #6c8db5;
}

.read-notification {
    background-color: #fafafa;
    opacity: 0.8;
}

/* 通知头部 */
.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.notification-title {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-title strong {
    font-size: 16px;
    color: #2c3e50;
}

.unread-indicator {
    font-size: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 操作按钮 */
.notification-actions {
    margin-left: 15px;
    flex-shrink: 0;
}

.mark-read-btn {
    background: #7ba05b;
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(123, 160, 91, 0.2);
}

.mark-read-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(123, 160, 91, 0.3);
    background: #6b8f52;
}

.read-status {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

/* 通知消息 */
.notification-message {
    font-size: 14px;
    line-height: 1.6;
    color: #495057;
    margin-bottom: 10px;
    padding: 10px 15px;
    background-color: rgba(248, 249, 250, 0.5);
    border-radius: 8px;
    border-left: 3px solid #e9ecef;
}

.read-notification .notification-message {
    background-color: rgba(241, 243, 244, 0.3);
}

/* 时间戳 */
.notification-time {
    font-size: 12px;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 徽章样式 */
.badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .notification-header {
        flex-direction: column;
        gap: 10px;
    }

    .notification-actions {
        margin-left: 0;
        align-self: flex-end;
    }

    .notification-content {
        padding: 15px;
    }

    .section-header {
        padding: 12px 15px;
    }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* 卡片头部美化 */
.card-header {
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: none;
    border-radius: 12px;
    overflow: hidden;
}
</style>
{% endblock %}
