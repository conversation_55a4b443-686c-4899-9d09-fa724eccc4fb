from typing import List, Dict, Any
from .base_agent import BaseAgent
from dao.material_dao import MaterialDAO
from services.notification_service import NotificationService


class SmartRecommendationAgent(BaseAgent):
    """智能推荐Agent"""

    def __init__(self):
        super().__init__("SmartRecommendation")
        self.material_dao = MaterialDAO()
        self.notification_service = NotificationService()

    def execute(self) -> Dict[str, Any]:
        """执行推荐任务"""
        results = {
            'recommendations_generated': 0,
            'users_processed': 0,
            'status': 'success'
        }

        try:
            # 获取活跃用户
            active_users = self.get_active_users()
            results['users_processed'] = len(active_users)

            # 为每个用户生成推荐
            total_recommendations = 0
            for user in active_users:
                user_recommendations = self.generate_user_recommendations(user['id'])
                if user_recommendations:
                    self.save_recommendations(user['id'], user_recommendations)
                    total_recommendations += len(user_recommendations)

            results['recommendations_generated'] = total_recommendations

            self.log_action("推荐生成完成", 
                          f"处理用户:{len(active_users)}, 生成推荐:{total_recommendations}")

        except Exception as e:
            self.logger.error(f"推荐生成失败: {str(e)}")
            results['error'] = str(e)
            results['status'] = 'failed'

        return results

    def get_active_users(self) -> List[Dict]:
        """获取活跃用户（只包括普通员工，排除管理员）"""
        try:
            sql = """
            SELECT DISTINCT u.id, u.username, u.department_id, u.real_name, u.role
            FROM users u
            WHERE u.role = 'employee'
            AND u.status = 'active'
            AND u.department_id IS NOT NULL
            """
            return self.material_dao.db.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取活跃用户失败: {e}")
            return []

    def generate_user_recommendations(self, user_id: int) -> List[Dict]:
        """为用户生成推荐（管理员和普通员工不同内容）"""
        try:
            # 首先检查用户角色
            user_info = self.get_user_info(user_id)
            if not user_info:
                return []

            # 管理员生成管理类推荐
            if user_info.get('role') == 'admin':
                return self.generate_admin_recommendations(user_id)

            # 普通员工生成物资申请推荐

            user_department_id = user_info.get('department_id')
            if not user_department_id:
                self.logger.info(f"用户 {user_id} 没有科室信息，跳过推荐")
                return []

            # 获取用户科室可申请的物资
            available_materials = self.get_department_available_materials(user_department_id)
            if not available_materials:
                self.logger.info(f"用户 {user_id} 所在科室没有可申请的物资")
                return []

            # 获取同科室其他用户的热门申请
            department_recommendations = self.get_department_popular_materials(user_id)

            # 获取全局热门物资（限制在用户科室可申请范围内）
            global_recommendations = self.get_global_popular_materials(user_id, user_department_id)

            # 合并推荐结果
            recommendations = []

            # 添加科室推荐（基于同科室用户的申请）
            for i, item in enumerate(department_recommendations[:3]):
                if self.is_material_available_for_department(item['id'], user_department_id):
                    # 根据申请次数动态计算评分
                    request_count = item.get('request_count', 1)
                    base_score = 0.8
                    popularity_bonus = min(0.15, request_count * 0.02)  # 最多加15%
                    final_score = min(0.95, base_score + popularity_bonus - i * 0.05)  # 排名越靠前分数越高

                    recommendations.append({
                        'material_id': item['id'],
                        'material_name': item['name'],
                        'category': item['category'],
                        'reason': f"您的科室同事经常申请{item['name']}（近期{request_count}次申请）",
                        'score': final_score,
                        'type': 'department'
                    })

            # 添加个人历史偏好推荐
            personal_recommendations = self.get_personal_recommendations(user_id, user_department_id)
            for i, item in enumerate(personal_recommendations[:2]):
                # 根据个人申请频率计算评分
                request_count = item.get('request_count', 1)
                base_score = 0.7
                frequency_bonus = min(0.2, request_count * 0.03)  # 最多加20%
                final_score = min(0.9, base_score + frequency_bonus - i * 0.03)

                recommendations.append({
                    'material_id': item['id'],
                    'material_name': item['name'],
                    'category': item['category'],
                    'reason': f"基于您的历史申请记录，推荐{item['name']}（您已申请{request_count}次）",
                    'score': final_score,
                    'type': 'personal'
                })

            # 添加全局推荐（限制在科室范围内）
            for i, item in enumerate(global_recommendations[:2]):
                if self.is_material_available_for_department(item['id'], user_department_id):
                    # 根据全局热度和用户科室匹配度计算评分
                    request_count = item.get('request_count', 1)
                    base_score = 0.5
                    global_popularity = min(0.2, request_count * 0.01)  # 全局热度加分
                    randomness = (hash(str(user_id) + str(item['id'])) % 20) / 100  # 添加个性化随机因子
                    final_score = min(0.75, base_score + global_popularity + randomness - i * 0.05)

                    recommendations.append({
                        'material_id': item['id'],
                        'material_name': item['name'],
                        'category': item['category'],
                        'reason': f"{item['name']}是最近的热门申请物资（全站{request_count}次申请）",
                        'score': final_score,
                        'type': 'global'
                    })

            # 去重并返回前5个推荐
            seen_materials = set()
            unique_recommendations = []
            for rec in recommendations:
                if rec['material_id'] not in seen_materials:
                    seen_materials.add(rec['material_id'])
                    unique_recommendations.append(rec)
                    if len(unique_recommendations) >= 5:
                        break

            return unique_recommendations

        except Exception as e:
            self.logger.error(f"生成用户推荐失败: {e}")
            return []

    def get_user_request_history(self, user_id: int) -> List[Dict]:
        """获取用户申请历史"""
        try:
            sql = """
            SELECT DISTINCT m.id, m.name, m.category
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.user_id = %s
            AND mr.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
            """
            return self.material_dao.db.execute_query(sql, (user_id,))
        except Exception as e:
            self.logger.error(f"获取用户申请历史失败: {e}")
            return []

    def get_department_popular_materials(self, user_id: int) -> List[Dict]:
        """获取科室热门物资"""
        try:
            sql = """
            SELECT m.id, m.name, m.category, COUNT(*) as request_count
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            JOIN users u ON mr.user_id = u.id
            WHERE u.department_id = (SELECT department_id FROM users WHERE id = %s)
            AND mr.user_id != %s
            AND mr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            AND m.status = 'available'
            AND m.remaining_quantity > 0
            GROUP BY m.id, m.name, m.category
            ORDER BY request_count DESC
            LIMIT 5
            """
            return self.material_dao.db.execute_query(sql, (user_id, user_id))
        except Exception as e:
            self.logger.error(f"获取科室热门物资失败: {e}")
            return []

    def get_global_popular_materials(self, user_id: int, department_id: int = None) -> List[Dict]:
        """获取全局热门物资（可限制科室范围）"""
        try:
            if department_id:
                # 限制在科室可申请的物资范围内
                sql = """
                SELECT DISTINCT m.id, m.name, m.category, COUNT(mr.id) as request_count
                FROM materials m
                LEFT JOIN material_requests mr ON m.id = mr.material_id
                    AND mr.user_id != %s
                    AND mr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                WHERE m.status = 'available'
                AND m.remaining_quantity > 0
                AND (m.id IN (
                    SELECT DISTINCT ma.material_id
                    FROM material_allocations ma
                    WHERE ma.department_id = %s
                ) OR m.category = 'consumable')
                GROUP BY m.id, m.name, m.category
                ORDER BY request_count DESC, m.created_at DESC
                LIMIT 5
                """
                return self.material_dao.db.execute_query(sql, (user_id, department_id))
            else:
                sql = """
                SELECT m.id, m.name, m.category, COUNT(*) as request_count
                FROM material_requests mr
                JOIN materials m ON mr.material_id = m.id
                WHERE mr.user_id != %s
                AND mr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                AND m.status = 'available'
                AND m.remaining_quantity > 0
                GROUP BY m.id, m.name, m.category
                ORDER BY request_count DESC
                LIMIT 5
                """
                return self.material_dao.db.execute_query(sql, (user_id,))
        except Exception as e:
            self.logger.error(f"获取全局热门物资失败: {e}")
            return []

    def save_recommendations(self, user_id: int, recommendations: List[Dict]):
        """保存推荐结果"""
        try:
            # 确保推荐表存在
            self._ensure_recommendations_table()
            
            # 清除用户旧的推荐
            delete_sql = "DELETE FROM user_recommendations WHERE user_id = %s"
            self.material_dao.db.execute_update(delete_sql, (user_id,))
            
            # 插入新推荐
            for rec in recommendations:
                insert_sql = """
                INSERT INTO user_recommendations
                (user_id, material_id, reason, score, type, created_at)
                VALUES (%s, %s, %s, %s, %s, NOW())
                """
                # 如果没有material_id，使用NULL
                material_id = rec.get('material_id') if rec.get('material_id') is not None else None

                self.material_dao.db.execute_insert(insert_sql, (
                    user_id,
                    material_id,
                    rec['reason'],
                    rec['score'],
                    rec['type']
                ))
                
        except Exception as e:
            self.logger.error(f"保存推荐失败: {e}")

    def _ensure_recommendations_table(self):
        """确保推荐表存在"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS user_recommendations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                material_id INT NOT NULL,
                reason TEXT,
                score DECIMAL(3,2) DEFAULT 0.5,
                type VARCHAR(50) DEFAULT 'general',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at)
            )
            """
            self.material_dao.db.execute_update(create_table_sql)
        except Exception as e:
            self.logger.error(f"创建推荐表失败: {e}")

    def get_user_recommendations(self, user_id: int, limit: int = 5) -> List[Dict]:
        """获取用户推荐"""
        try:
            sql = """
            SELECT ur.*, m.name as material_name, m.category, m.remaining_quantity
            FROM user_recommendations ur
            JOIN materials m ON ur.material_id = m.id
            WHERE ur.user_id = %s
            AND m.status = 'available'
            AND m.remaining_quantity > 0
            ORDER BY ur.score DESC, ur.created_at DESC
            LIMIT %s
            """
            return self.material_dao.db.execute_query(sql, (user_id, limit))
        except Exception as e:
            self.logger.error(f"获取用户推荐失败: {e}")
            return []

    def get_user_info(self, user_id: int) -> Dict:
        """获取用户信息"""
        try:
            sql = "SELECT id, username, role, department_id, real_name FROM users WHERE id = %s"
            result = self.material_dao.db.execute_query(sql, (user_id,))
            return result[0] if result else {}
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return {}

    def get_department_available_materials(self, department_id: int) -> List[Dict]:
        """获取科室可申请的物资"""
        try:
            sql = """
            SELECT DISTINCT m.id, m.name, m.category
            FROM materials m
            WHERE m.status = 'available'
            AND m.remaining_quantity > 0
            AND (
                m.id IN (
                    SELECT DISTINCT ma.material_id
                    FROM material_allocations ma
                    WHERE ma.department_id = %s
                )
                OR m.category = 'consumable'
            )
            """
            return self.material_dao.db.execute_query(sql, (department_id,))
        except Exception as e:
            self.logger.error(f"获取科室可申请物资失败: {e}")
            return []

    def is_material_available_for_department(self, material_id: int, department_id: int) -> bool:
        """检查物资是否对科室可用"""
        try:
            sql = """
            SELECT 1 FROM materials m
            WHERE m.id = %s
            AND m.status = 'available'
            AND m.remaining_quantity > 0
            AND (
                m.id IN (
                    SELECT DISTINCT ma.material_id
                    FROM material_allocations ma
                    WHERE ma.department_id = %s
                )
                OR m.category = 'consumable'
            )
            """
            result = self.material_dao.db.execute_query(sql, (material_id, department_id))
            return len(result) > 0
        except Exception as e:
            self.logger.error(f"检查物资可用性失败: {e}")
            return False

    def get_personal_recommendations(self, user_id: int, department_id: int) -> List[Dict]:
        """基于用户历史获取个人推荐"""
        try:
            sql = """
            SELECT DISTINCT m.id, m.name, m.category, COUNT(*) as request_count
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.user_id = %s
            AND mr.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
            AND m.status = 'available'
            AND m.remaining_quantity > 0
            AND (
                m.id IN (
                    SELECT DISTINCT ma.material_id
                    FROM material_allocations ma
                    WHERE ma.department_id = %s
                )
                OR m.category = 'consumable'
            )
            GROUP BY m.id, m.name, m.category
            ORDER BY request_count DESC
            LIMIT 3
            """
            return self.material_dao.db.execute_query(sql, (user_id, department_id))
        except Exception as e:
            self.logger.error(f"获取个人推荐失败: {e}")
            return []

    def generate_admin_recommendations(self, user_id: int) -> List[Dict]:
        """为管理员生成管理类推荐"""
        try:
            recommendations = []

            # 1. 库存预警推荐
            low_stock_items = self.get_low_stock_materials()
            if low_stock_items:
                # 根据库存紧急程度计算评分
                critical_items = [item for item in low_stock_items if item.get('remaining_quantity', 0) <= 2]
                urgency_score = 0.9 if critical_items else 0.8
                urgency_score += min(0.1, len(low_stock_items) * 0.01)  # 物品数量越多越紧急

                recommendations.append({
                    'material_id': None,
                    'material_name': '库存预警',
                    'category': 'management',
                    'reason': f'发现 {len(low_stock_items)} 项物资库存不足，其中 {len(critical_items)} 项极度紧急',
                    'score': min(0.95, urgency_score),
                    'type': 'inventory_alert',
                    'action_url': '/reports/materials?category=low_stock',
                    'data': low_stock_items[:5]  # 只显示前5项
                })

            # 2. 待审批申请提醒
            pending_requests = self.get_pending_requests()
            if pending_requests:
                # 根据待审批数量和时间紧急程度计算评分
                urgent_requests = [req for req in pending_requests if self._is_request_urgent(req)]
                base_score = 0.8
                quantity_bonus = min(0.15, len(pending_requests) * 0.02)
                urgency_bonus = min(0.1, len(urgent_requests) * 0.03)
                final_score = min(0.95, base_score + quantity_bonus + urgency_bonus)

                recommendations.append({
                    'material_id': None,
                    'material_name': '待审批申请',
                    'category': 'management',
                    'reason': f'有 {len(pending_requests)} 个物资申请等待审批，其中 {len(urgent_requests)} 个较紧急',
                    'score': final_score,
                    'type': 'approval_reminder',
                    'action_url': '/requests',
                    'data': pending_requests[:3]  # 只显示前3项
                })

            # 3. 热门申请物资分析
            popular_materials = self.get_popular_materials_analysis()
            if popular_materials:
                top_material = popular_materials[0]
                recommendations.append({
                    'material_id': top_material['id'],
                    'material_name': f"热门物资：{top_material['name']}",
                    'category': 'management',
                    'reason': f'最近30天申请次数最多的物资，共 {top_material["request_count"]} 次申请',
                    'score': 0.8,
                    'type': 'popular_analysis',
                    'action_url': f'/materials/{top_material["id"]}',
                    'data': popular_materials[:5]
                })

            # 4. 科室使用统计分析
            department_stats = self.get_department_usage_stats()
            if department_stats:
                top_dept = department_stats[0]
                recommendations.append({
                    'material_id': None,
                    'material_name': '科室使用分析',
                    'category': 'management',
                    'reason': f'{top_dept["department_name"]} 是物资使用最活跃的科室',
                    'score': 0.75,
                    'type': 'department_analysis',
                    'action_url': '/reports/departments',
                    'data': department_stats[:3]
                })

            # 5. 成本控制建议
            cost_analysis = self.get_cost_analysis()
            if cost_analysis:
                recommendations.append({
                    'material_id': None,
                    'material_name': '成本控制建议',
                    'category': 'management',
                    'reason': f'本月物资支出较上月变化 {cost_analysis["change_percent"]:.1f}%',
                    'score': 0.7,
                    'type': 'cost_analysis',
                    'action_url': '/reports/cost',
                    'data': cost_analysis
                })

            return recommendations[:5]  # 返回前5个推荐

        except Exception as e:
            self.logger.error(f"生成管理员推荐失败: {e}")
            return []

    def _is_request_urgent(self, request):
        """判断申请是否紧急（超过2天未处理）"""
        try:
            from datetime import datetime, timedelta
            created_at = request.get('created_at')
            if created_at:
                if isinstance(created_at, str):
                    created_at = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                return datetime.now() - created_at > timedelta(days=2)
            return False
        except:
            return False

    def get_low_stock_materials(self) -> List[Dict]:
        """获取低库存物资"""
        try:
            sql = """
            SELECT id, name, category, remaining_quantity, quantity,
                   ROUND((remaining_quantity / quantity) * 100, 1) as stock_percentage
            FROM materials
            WHERE status = 'available'
            AND remaining_quantity > 0
            AND (remaining_quantity / quantity) <= 0.2
            ORDER BY stock_percentage ASC
            LIMIT 10
            """
            return self.material_dao.db.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取低库存物资失败: {e}")
            return []

    def get_pending_requests(self) -> List[Dict]:
        """获取待审批申请"""
        try:
            sql = """
            SELECT mr.id, mr.quantity, mr.created_at,
                   m.name as material_name, m.category,
                   u.real_name as applicant_name, d.name as department_name
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            JOIN users u ON mr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE mr.status = 'pending'
            ORDER BY mr.created_at ASC
            LIMIT 10
            """
            return self.material_dao.db.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取待审批申请失败: {e}")
            return []

    def get_popular_materials_analysis(self) -> List[Dict]:
        """获取热门物资分析"""
        try:
            sql = """
            SELECT m.id, m.name, m.category, COUNT(mr.id) as request_count,
                   SUM(mr.quantity) as total_quantity
            FROM materials m
            JOIN material_requests mr ON m.id = mr.material_id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY m.id, m.name, m.category
            ORDER BY request_count DESC
            LIMIT 5
            """
            return self.material_dao.db.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取热门物资分析失败: {e}")
            return []

    def get_department_usage_stats(self) -> List[Dict]:
        """获取科室使用统计"""
        try:
            sql = """
            SELECT d.name as department_name, COUNT(mr.id) as request_count,
                   SUM(mr.quantity) as total_quantity,
                   COUNT(DISTINCT mr.material_id) as unique_materials
            FROM departments d
            JOIN users u ON d.id = u.department_id
            JOIN material_requests mr ON u.id = mr.user_id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY d.id, d.name
            ORDER BY request_count DESC
            LIMIT 5
            """
            return self.material_dao.db.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取科室使用统计失败: {e}")
            return []

    def get_cost_analysis(self) -> Dict:
        """获取成本分析"""
        try:
            # 本月成本
            current_month_sql = """
            SELECT SUM(mr.quantity * m.price) as total_cost
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.status = 'approved'
            AND YEAR(mr.created_at) = YEAR(NOW())
            AND MONTH(mr.created_at) = MONTH(NOW())
            """
            current_result = self.material_dao.db.execute_query(current_month_sql)
            current_cost = current_result[0]['total_cost'] or 0

            # 上月成本
            last_month_sql = """
            SELECT SUM(mr.quantity * m.price) as total_cost
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.status = 'approved'
            AND YEAR(mr.created_at) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))
            AND MONTH(mr.created_at) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))
            """
            last_result = self.material_dao.db.execute_query(last_month_sql)
            last_cost = last_result[0]['total_cost'] or 0

            # 计算变化百分比
            if last_cost > 0:
                change_percent = ((current_cost - last_cost) / last_cost) * 100
            else:
                change_percent = 0

            return {
                'current_month_cost': float(current_cost),
                'last_month_cost': float(last_cost),
                'change_percent': change_percent,
                'change_amount': float(current_cost - last_cost)
            }
        except Exception as e:
            self.logger.error(f"获取成本分析失败: {e}")
            return {}
