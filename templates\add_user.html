{% extends "base.html" %}

{% block title %}添加用户{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">添加用户</h1>
        <p>创建新的系统用户账户</p>
    </div>
    
    <div class="card-body">
        <form method="POST" id="addUserForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="username" class="form-label">用户名 <span class="required">*</span></label>
                        <input type="text" id="username" name="username" class="form-control" required>
                        <small class="form-text">用于登录系统的用户名，不能重复</small>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="real_name" class="form-label">真实姓名 <span class="required">*</span></label>
                        <input type="text" id="real_name" name="real_name" class="form-control" required>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="password" class="form-label">密码 <span class="required">*</span></label>
                        <input type="password" id="password" name="password" class="form-control" required>
                        <small class="form-text">密码长度至少6位</small>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">确认密码 <span class="required">*</span></label>
                        <input type="password" id="confirm_password" class="form-control" required>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="role" class="form-label">角色 <span class="required">*</span></label>
                        <select id="role" name="role" class="form-control" required>
                            <option value="">请选择角色</option>
                            <option value="employee">员工</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="department_id" class="form-label">科室</label>
                        <select id="department_id" name="department_id" class="form-control">
                            <option value="">请选择科室</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" id="email" name="email" class="form-control">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="phone" class="form-label">电话</label>
                        <input type="tel" id="phone" name="phone" class="form-control">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="status" class="form-label">状态</label>
                        <select id="status" name="status" class="form-control">
                            <option value="active">活跃</option>
                            <option value="inactive">禁用</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">创建用户</button>
                <a href="{{ url_for('user.user_list') }}" class="btn btn-secondary">取消</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    var password = document.getElementById('password').value;
    var confirmPassword = document.getElementById('confirm_password').value;
    
    // 验证密码
    if (password !== confirmPassword) {
        e.preventDefault();
        alert('两次输入的密码不一致！');
        return false;
    }
    
    if (password.length < 6) {
        e.preventDefault();
        alert('密码长度至少6位！');
        return false;
    }
    
    // 验证用户名
    var username = document.getElementById('username').value;
    if (username.length < 3) {
        e.preventDefault();
        alert('用户名长度至少3位！');
        return false;
    }
    
    // 验证真实姓名
    var realName = document.getElementById('real_name').value;
    if (realName.trim().length < 2) {
        e.preventDefault();
        alert('请输入有效的真实姓名！');
        return false;
    }
});
</script>
{% endblock %}
