#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

def check_recommendations():
    """检查推荐相关表"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='lax217652',
            database='goods'
        )
        cursor = conn.cursor()
        
        print("=== 检查数据库表 ===")
        cursor.execute('SHOW TABLES')
        tables = cursor.fetchall()
        table_names = [t[0] for t in tables]
        print('所有表:', table_names)
        
        print("\n=== 检查user_recommendations表 ===")
        if 'user_recommendations' in table_names:
            print("✅ user_recommendations表存在")
            
            # 检查表结构
            cursor.execute('DESCRIBE user_recommendations')
            columns = cursor.fetchall()
            print("表结构:")
            for col in columns:
                print(f"  {col[0]} - {col[1]}")
            
            # 检查数据
            cursor.execute('SELECT COUNT(*) FROM user_recommendations')
            count = cursor.fetchone()[0]
            print(f"推荐记录数量: {count}")
            
            if count > 0:
                cursor.execute('SELECT * FROM user_recommendations LIMIT 5')
                records = cursor.fetchall()
                print("前5条记录:")
                for record in records:
                    print(f"  {record}")
        else:
            print("❌ user_recommendations表不存在")
            
        print("\n=== 检查agent_config表 ===")
        if 'agent_config' in table_names:
            print("✅ agent_config表存在")
            cursor.execute('SELECT * FROM agent_config')
            configs = cursor.fetchall()
            print(f"配置记录数量: {len(configs)}")
            for config in configs:
                print(f"  {config}")
        else:
            print("❌ agent_config表不存在")
            
        print("\n=== 检查notifications表 ===")
        if 'notifications' in table_names:
            print("✅ notifications表存在")
            cursor.execute('SELECT COUNT(*) FROM notifications')
            count = cursor.fetchone()[0]
            print(f"通知记录数量: {count}")
        else:
            print("❌ notifications表不存在")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == '__main__':
    check_recommendations()
