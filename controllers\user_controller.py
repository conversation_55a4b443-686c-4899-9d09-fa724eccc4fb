from flask import Blueprint, request, render_template, redirect, url_for, session, flash, jsonify
from controllers.auth_controller import login_required, admin_required, get_current_user
from services.user_service import UserService
from dao.department_dao import DepartmentDAO
from models.user import User
from datetime import datetime

user_bp = Blueprint('user', __name__)
user_service = UserService()
department_dao = DepartmentDAO()

@user_bp.route('/users')
@admin_required
def user_list():
    """用户列表"""
    try:
        # 获取搜索和筛选参数
        keyword = request.args.get('keyword', '').strip()
        role = request.args.get('role', '')
        department_id = request.args.get('department_id', '')
        status = request.args.get('status', '')
        
        # 构建筛选条件
        filters = {
            'keyword': keyword,
            'role': role,
            'department_id': int(department_id) if department_id else None,
            'status': status
        }
        
        # 获取用户列表
        users = user_service.get_users_with_filters(filters)
        
        # 获取科室列表用于筛选
        departments = department_dao.get_all_departments()
        
        return render_template('user_list.html', 
                             users=users, 
                             departments=departments,
                             filters=filters)
    except Exception as e:
        flash(f'获取用户列表失败：{str(e)}', 'error')
        return redirect(url_for('material.dashboard'))

@user_bp.route('/users/add', methods=['GET', 'POST'])
@admin_required
def add_user():
    """添加用户"""
    if request.method == 'POST':
        try:
            user_data = {
                'username': request.form.get('username'),
                'password': request.form.get('password'),
                'real_name': request.form.get('real_name'),
                'role': request.form.get('role'),
                'department_id': int(request.form.get('department_id')) if request.form.get('department_id') else None,
                'email': request.form.get('email'),
                'phone': request.form.get('phone'),
                'status': request.form.get('status', 'active')
            }
            
            # 验证必填字段
            if not all([user_data['username'], user_data['password'], user_data['real_name'], user_data['role']]):
                flash('请填写所有必填字段', 'error')
                return render_template('add_user.html', departments=department_dao.get_all_departments())
            
            user_service.create_user(user_data)
            flash('用户创建成功', 'success')
            return redirect(url_for('user.user_list'))
            
        except Exception as e:
            flash(f'创建用户失败：{str(e)}', 'error')
    
    departments = department_dao.get_all_departments()
    return render_template('add_user.html', departments=departments)

@user_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_user(user_id):
    """编辑用户"""
    if request.method == 'POST':
        try:
            user_data = {
                'real_name': request.form.get('real_name'),
                'role': request.form.get('role'),
                'department_id': int(request.form.get('department_id')) if request.form.get('department_id') else None,
                'email': request.form.get('email'),
                'phone': request.form.get('phone'),
                'status': request.form.get('status')
            }
            
            user_service.update_user(user_id, user_data)
            flash('用户信息更新成功', 'success')
            return redirect(url_for('user.user_list'))
            
        except Exception as e:
            flash(f'更新用户失败：{str(e)}', 'error')
    
    try:
        user = user_service.get_user_detail(user_id)
        departments = department_dao.get_all_departments()
        return render_template('edit_user.html', user=user, departments=departments)
    except Exception as e:
        flash(f'获取用户信息失败：{str(e)}', 'error')
        return redirect(url_for('user.user_list'))

@user_bp.route('/users/<int:user_id>/delete', methods=['POST'])
@admin_required
def delete_user(user_id):
    """删除用户"""
    try:
        current_user = get_current_user()
        if current_user['id'] == user_id:
            flash('不能删除自己的账户', 'error')
            return redirect(url_for('user.user_list'))
        
        user_service.delete_user(user_id)
        flash('用户删除成功', 'success')
    except Exception as e:
        flash(f'删除用户失败：{str(e)}', 'error')
    
    return redirect(url_for('user.user_list'))

@user_bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@admin_required
def toggle_user_status(user_id):
    """切换用户状态（启用/禁用）"""
    try:
        current_user = get_current_user()
        if current_user['id'] == user_id:
            flash('不能禁用自己的账户', 'error')
            return redirect(url_for('user.user_list'))
        
        user_service.toggle_user_status(user_id)
        flash('用户状态更新成功', 'success')
    except Exception as e:
        flash(f'更新用户状态失败：{str(e)}', 'error')
    
    return redirect(url_for('user.user_list'))

@user_bp.route('/users/<int:user_id>/reset-password', methods=['POST'])
@admin_required
def reset_password(user_id):
    """重置用户密码"""
    try:
        new_password = request.form.get('new_password')
        if not new_password:
            flash('请输入新密码', 'error')
            return redirect(url_for('user.user_list'))
        
        user_service.reset_password(user_id, new_password)
        flash('密码重置成功', 'success')
    except Exception as e:
        flash(f'重置密码失败：{str(e)}', 'error')
    
    return redirect(url_for('user.user_list'))

@user_bp.route('/users/<int:user_id>')
@admin_required
def user_detail(user_id):
    """用户详情"""
    try:
        user = user_service.get_user_detail(user_id)
        return render_template('user_detail.html', user=user)
    except Exception as e:
        flash(f'获取用户详情失败：{str(e)}', 'error')
        return redirect(url_for('user.user_list'))
