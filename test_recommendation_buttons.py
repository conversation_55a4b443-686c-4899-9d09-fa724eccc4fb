#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试推荐页面按钮功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.agent_service import AgentService
import pymysql

def test_material_routes():
    """测试物资相关路由"""
    try:
        print("=== 测试物资路由 ===")
        
        # 检查数据库中的物资
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='lax217652',
            database='goods',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        cursor = conn.cursor()
        
        # 查询推荐中的物资ID
        cursor.execute("""
            SELECT DISTINCT ur.material_id, m.name, m.status, m.remaining_quantity
            FROM user_recommendations ur
            LEFT JOIN materials m ON ur.material_id = m.id
            WHERE ur.material_id IS NOT NULL
            ORDER BY ur.material_id
            LIMIT 5
        """)
        
        materials = cursor.fetchall()
        
        print(f"推荐中的物资数量: {len(materials)}")
        
        for material in materials:
            material_id = material['material_id']
            name = material['name']
            status = material['status']
            remaining = material['remaining_quantity']
            
            print(f"\n物资ID: {material_id}")
            print(f"  名称: {name}")
            print(f"  状态: {status}")
            print(f"  剩余数量: {remaining}")
            
            # 检查物资是否可以申请
            if status == 'available' and remaining > 0:
                print(f"  ✅ 可以申请")
                print(f"  详情页面URL: /materials/{material_id}")
                print(f"  申请API URL: /api/materials/{material_id}/request")
            else:
                print(f"  ❌ 不可申请 (状态: {status}, 剩余: {remaining})")
        
        conn.close()
        
        # 测试推荐数据
        print("\n=== 测试推荐数据 ===")
        agent_service = AgentService()
        
        # 获取普通用户推荐
        user_recs = agent_service.get_user_recommendations(2)
        print(f"普通用户推荐数量: {len(user_recs)}")
        
        for i, rec in enumerate(user_recs[:3], 1):
            material_id = rec.get('material_id')
            material_name = rec.get('material_name')
            category = rec.get('category')
            remaining = rec.get('remaining_quantity')
            
            print(f"\n{i}. 推荐物资:")
            print(f"   ID: {material_id}")
            print(f"   名称: {material_name}")
            print(f"   类别: {category}")
            print(f"   剩余: {remaining}")
            
            if material_id:
                print(f"   详情链接: /materials/{material_id}")
                print(f"   申请API: /api/materials/{material_id}/request")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_material_routes()
