#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.smart_recommendation_agent import SmartRecommendationAgent
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)

def run_smart_recommendations():
    """运行智能推荐算法"""
    print("=== 启动智能推荐算法 ===")
    
    try:
        # 创建推荐Agent
        agent = SmartRecommendationAgent()
        
        # 执行推荐
        result = agent.execute()
        
        print(f"推荐算法执行结果:")
        print(f"  状态: {result['status']}")
        print(f"  处理用户数: {result['users_processed']}")
        print(f"  生成推荐数: {result['recommendations_generated']}")
        
        if result['status'] == 'failed':
            print(f"  错误信息: {result.get('error', '未知错误')}")
        
        # 验证推荐结果
        print("\n=== 验证推荐结果 ===")
        import pymysql
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='qyf20031211',
            database='goods'
        )
        cursor = conn.cursor()
        
        # 检查推荐数量
        cursor.execute("SELECT COUNT(*) FROM user_recommendations")
        total_recs = cursor.fetchone()[0]
        print(f"总推荐记录数: {total_recs}")
        
        # 显示每个用户的推荐
        cursor.execute("""
        SELECT u.username, COUNT(ur.id) as rec_count
        FROM users u
        LEFT JOIN user_recommendations ur ON u.id = ur.user_id
        GROUP BY u.id, u.username
        ORDER BY rec_count DESC
        """)
        user_stats = cursor.fetchall()
        print("\n各用户推荐数量:")
        for stat in user_stats:
            print(f"  {stat[0]}: {stat[1]} 条推荐")
        
        # 显示推荐详情示例
        cursor.execute("""
        SELECT u.username, m.name, ur.reason, ur.score, ur.type
        FROM user_recommendations ur
        JOIN users u ON ur.user_id = u.id
        JOIN materials m ON ur.material_id = m.id
        ORDER BY ur.score DESC
        LIMIT 10
        """)
        sample_recs = cursor.fetchall()
        print("\n推荐示例 (前10条):")
        for rec in sample_recs:
            print(f"  {rec[0]} -> {rec[1]} (评分: {rec[3]}, 类型: {rec[4]})")
            print(f"    理由: {rec[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 运行智能推荐失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    run_smart_recommendations()
