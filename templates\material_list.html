{% extends "base.html" %}

{% block title %}物资列表 - 金融企业物资管理系统{% endblock %}

{% block extra_css %}
<style>
/* 优化表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background: var(--primary-color);
    color: white;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border: none;
}

.table td {
    padding: 12px 8px;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.table tbody tr:hover {
    background-color: var(--light-color);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 操作按钮样式 */
.btn-group {
    display: flex;
    gap: 5px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.8rem;
    border-radius: 6px;
    min-width: 50px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .table th, .table td {
        padding: 8px 4px;
        font-size: 0.8rem;
    }

    .btn-sm {
        padding: 3px 6px;
        font-size: 0.75rem;
        min-width: 45px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">物资列表</h1>
        
        <!-- 搜索和筛选 -->
        <form method="GET" class="mt-2">
            <div class="d-flex gap-2 align-items-center flex-wrap">
                <div class="form-group mb-0">
                    <input type="text" name="keyword" class="form-control" placeholder="搜索物资名称、型号、供应商..."
                           value="{{ filters.keyword or '' }}">
                </div>

                <div class="form-group mb-0">
                    <select name="category" class="form-control">
                        <option value="">全部类别</option>
                        <option value="fixed_asset" {% if filters.category == 'fixed_asset' %}selected{% endif %}>固定资产</option>
                        <option value="consumable" {% if filters.category == 'consumable' %}selected{% endif %}>耗材</option>
                    </select>
                </div>

                {% if user.role != 'admin' %}
                <div class="form-group mb-0">
                    <select name="allocation_status" class="form-control">
                        <option value="">全部物资</option>
                        <option value="allocated" {% if filters.allocation_status == 'allocated' %}selected{% endif %}>已分配物资</option>
                        <option value="available" {% if filters.allocation_status == 'available' %}selected{% endif %}>可申请物资</option>
                    </select>
                </div>
                {% endif %}

                {% if user.role == 'admin' and departments %}
                <div class="form-group mb-0">
                    <select name="department_id" class="form-control">
                        <option value="">全部科室</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if filters.department_id == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}

                <div class="form-group mb-0">
                    <input type="number" name="min_price" class="form-control" placeholder="最低价格"
                           value="{{ filters.min_price or '' }}" step="0.01">
                </div>

                <div class="form-group mb-0">
                    <input type="number" name="max_price" class="form-control" placeholder="最高价格"
                           value="{{ filters.max_price or '' }}" step="0.01">
                </div>

                <button type="submit" class="btn btn-primary">搜索</button>
                <a href="{{ url_for('material.material_list') }}" class="btn btn-secondary">重置</a>
            </div>
        </form>
    </div>
    
    {% if user.role == 'admin' %}
    <div class="mb-2 d-flex gap-2 align-items-center">
        <a href="{{ url_for('material.add_material') }}" class="btn btn-success">添加物资</a>
        <button type="button" class="btn btn-primary" id="importBtn">批量导入</button>
    </div>
    {% endif %}
    
    {% if materials %}
    <table class="table">
        <thead>
            <tr>
                <th>资产编号</th>
                <th>物资名称</th>
                <th>型号</th>
                <th>类别</th>
                <th>单价</th>
                <th>数量</th>
                <th>剩余数量</th>
                <th>状态</th>
                <th>购入日期</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for material in materials %}
            <tr>
                <td>{{ material.asset_number or '-' }}</td>
                <td>
                    {{ material.name }}
                    {% if user.role != 'admin' %}
                        {% if material.is_allocated_to_department %}
                            <span class="badge badge-success" style="font-size: 0.7rem; margin-left: 5px;">已分配</span>
                        {% elif material.category == 'consumable' %}
                            <span class="badge badge-info" style="font-size: 0.7rem; margin-left: 5px;">可申请</span>
                        {% endif %}
                    {% endif %}
                </td>
                <td>{{ material.model or '-' }}</td>
                <td>
                    {% if material.category == 'fixed_asset' %}
                        <span style="color: var(--primary-color); font-weight: bold;">固定资产</span>
                    {% else %}
                        <span style="color: var(--accent-color); font-weight: bold;">耗材</span>
                    {% endif %}
                </td>
                <td>¥{{ "%.2f"|format(material.price) }}</td>
                <td>{{ material.quantity }}</td>
                <td>{{ material.remaining_quantity }}</td>
                <td>
                    {% if material.status == 'available' %}
                        <span style="color: var(--accent-color);">可用</span>
                    {% elif material.status == 'in_use' %}
                        <span style="color: var(--warning-color);">在用</span>
                    {% else %}
                        <span style="color: var(--danger-color);">已报废</span>
                    {% endif %}
                </td>
                <td>{{ material.purchase_date if material.purchase_date else '-' }}</td>
                <td>
                    <div class="btn-group">
                        <a href="{{ url_for('material.material_detail', material_id=material.id) }}"
                           class="btn btn-info btn-sm">详情</a>

                        {% if user.role == 'admin' %}
                            <a href="{{ url_for('material.allocate_material', material_id=material.id) }}"
                               class="btn btn-primary btn-sm">分配</a>
                            <a href="{{ url_for('material.edit_material', material_id=material.id) }}"
                               class="btn btn-warning btn-sm">编辑</a>
                            <button type="button" class="btn btn-danger btn-sm"
                                    onclick="confirmDelete({{ material.id }}, '{{ material.name }}')">删除</button>
                        {% else %}
                            {% if material.can_request %}
                            <button type="button" class="btn btn-warning btn-sm"
                                    onclick="showRequestModal({{ material.id }}, '{{ material.name }}')">申请</button>
                            {% elif material.remaining_quantity <= 0 %}
                            <button type="button" class="btn btn-secondary btn-sm" disabled>库存不足</button>
                            {% elif material.category == 'fixed_asset' and not material.is_allocated_to_department %}
                            <button type="button" class="btn btn-secondary btn-sm" disabled>需先分配</button>
                            {% else %}
                            <button type="button" class="btn btn-secondary btn-sm" disabled>不可申请</button>
                            {% endif %}
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center mt-3">
        <p>暂无物资数据</p>
        {% if user.role == 'admin' %}
        <a href="{{ url_for('material.add_material') }}" class="btn btn-success">添加第一个物资</a>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- 申请物资模态框 -->
{% if user.role != 'admin' %}
<div id="requestModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 15px; width: 90%; max-width: 500px;">
        <h3>申请物资</h3>
        <form id="requestForm" method="POST">
            <div class="form-group">
                <label class="form-label">物资名称</label>
                <input type="text" id="materialName" class="form-control" readonly>
            </div>
            
            <div class="form-group">
                <label for="quantity" class="form-label">申请数量</label>
                <input type="number" id="quantity" name="quantity" class="form-control" min="1" value="1" required>
            </div>
            
            <div class="form-group">
                <label for="reason" class="form-label">申请理由</label>
                <textarea id="reason" name="reason" class="form-control" rows="3" required></textarea>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">提交申请</button>
                <button type="button" class="btn btn-secondary" onclick="hideRequestModal()">取消</button>
            </div>
        </form>
    </div>
</div>





{% endif %}

{% block extra_js %}
<script>
// 物资管理页面的JavaScript代码
console.log('Material list extra JS loaded');

// 物资申请相关函数
function showRequestModal(materialId, materialName) {
    document.getElementById('materialName').value = materialName;
    document.getElementById('requestForm').action = '/materials/' + materialId + '/request';
    document.getElementById('requestModal').style.display = 'block';
}

function hideRequestModal() {
    document.getElementById('requestModal').style.display = 'none';
}

// 删除确认函数
function confirmDelete(materialId, materialName) {
    if (confirm('确定要删除物资 "' + materialName + '" 吗？\n\n注意：删除后无法恢复，相关的分配记录也会被删除。')) {
        // 创建表单并提交
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/materials/' + materialId + '/delete';
        document.body.appendChild(form);
        form.submit();
    }
}

function showImportModal() {
    console.log('showImportModal called');
    const modal = document.getElementById('importModal');
    console.log('Import modal element:', modal);
    if (modal) {
        modal.style.display = 'block';
        console.log('Modal displayed');
    } else {
        console.error('Import modal not found');
    }
}

function hideImportModal() {
    const modal = document.getElementById('importModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('Material list page DOMContentLoaded');

    // 绑定导入按钮事件
    const importBtn = document.getElementById('importBtn');
    if (importBtn) {
        console.log('Binding import button');
        importBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Import button clicked');
            showImportModal();
        });
    }
});
</script>
{% endblock %}

<!-- 批量导入模态框 -->
{% if user.role == 'admin' %}
<div id="importModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 15px; width: 90%; max-width: 600px;">
        <h3>批量导入物资</h3>
        <form id="importForm" method="POST" action="/materials/batch-import" enctype="multipart/form-data">
            <div class="form-group">
                <label class="form-label">选择Excel文件</label>
                <input type="file" name="file" class="form-control" accept=".xlsx,.xls" required>
                <small class="form-text text-muted">支持 .xlsx 和 .xls 格式</small>
            </div>

            <div class="alert alert-info">
                <h6>Excel文件格式要求：</h6>
                <p>请确保Excel文件包含以下列（按顺序）：</p>
                <ol>
                    <li>物资名称 (必填)</li>
                    <li>型号</li>
                    <li>类别 (fixed_asset/consumable)</li>
                    <li>单价 (必填)</li>
                    <li>购入日期 (YYYY-MM-DD格式)</li>
                    <li>供应商</li>
                    <li>采购金额</li>
                    <li>数量 (必填)</li>
                    <li>描述</li>
                </ol>
                <p><a href="/static/template/物资导入模板.xlsx" download>下载模板文件</a></p>
            </div>

            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">开始导入</button>
                <button type="button" class="btn btn-secondary" onclick="hideImportModal()">取消</button>
            </div>
        </form>
    </div>
</div>
{% endif %}
{% endblock %}
