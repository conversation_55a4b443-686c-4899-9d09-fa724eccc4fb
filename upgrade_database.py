#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
from config import Config

def upgrade_database():
    """升级数据库，添加金融功能相关字段和表"""
    try:
        conn = pymysql.connect(**Config.DB_CONFIG)
        cursor = conn.cursor()
        
        print("开始升级数据库...")
        
        # 检查materials表是否已有金融字段
        cursor.execute("SHOW COLUMNS FROM materials LIKE 'depreciation_method'")
        if not cursor.fetchone():
            print("为materials表添加金融相关字段...")
            
            # 添加金融相关字段
            alter_sql = """
            ALTER TABLE materials 
            ADD COLUMN depreciation_method ENUM('straight_line', 'declining_balance', 'sum_of_years') DEFAULT 'straight_line' COMMENT '折旧方法：直线法/余额递减法/年数总和法',
            ADD COLUMN useful_life_years INT DEFAULT 5 COMMENT '使用年限（年）',
            ADD COLUMN residual_value DECIMAL(10,2) DEFAULT 0 COMMENT '残值',
            ADD COLUMN accumulated_depreciation DECIMAL(10,2) DEFAULT 0 COMMENT '累计折旧',
            ADD COLUMN usage_count INT DEFAULT 0 COMMENT '使用次数',
            ADD COLUMN total_usage_hours DECIMAL(10,2) DEFAULT 0 COMMENT '总使用时长（小时）'
            """
            cursor.execute(alter_sql)
            print("✓ materials表字段添加完成")
        else:
            print("✓ materials表金融字段已存在")
        
        # 创建企业财务数据表
        cursor.execute("SHOW TABLES LIKE 'company_financials'")
        if not cursor.fetchone():
            print("创建企业财务数据表...")
            
            create_financials_sql = """
            CREATE TABLE company_financials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                year INT NOT NULL COMMENT '年份',
                month INT NOT NULL COMMENT '月份',
                monthly_operating_cash_flow DECIMAL(15,2) NOT NULL COMMENT '月度经营现金流',
                total_assets DECIMAL(15,2) NOT NULL COMMENT '总资产',
                average_asset_turnover_rate DECIMAL(5,4) DEFAULT 1.0 COMMENT '企业平均资产周转率',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_year_month (year, month)
            ) COMMENT='企业财务数据表'
            """
            cursor.execute(create_financials_sql)
            print("✓ 企业财务数据表创建完成")
        else:
            print("✓ 企业财务数据表已存在")
        
        # 创建耗材采购现金流表
        cursor.execute("SHOW TABLES LIKE 'consumable_purchases'")
        if not cursor.fetchone():
            print("创建耗材采购现金流表...")
            
            create_purchases_sql = """
            CREATE TABLE consumable_purchases (
                id INT AUTO_INCREMENT PRIMARY KEY,
                material_id INT NOT NULL COMMENT '耗材ID',
                purchase_date DATE NOT NULL COMMENT '采购日期',
                quantity INT NOT NULL COMMENT '采购数量',
                unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
                total_amount DECIMAL(12,2) NOT NULL COMMENT '总金额',
                supplier VARCHAR(200) COMMENT '供应商',
                payment_method ENUM('cash', 'bank_transfer', 'credit') DEFAULT 'bank_transfer' COMMENT '付款方式',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE
            ) COMMENT='耗材采购现金流表'
            """
            cursor.execute(create_purchases_sql)
            print("✓ 耗材采购现金流表创建完成")
        else:
            print("✓ 耗材采购现金流表已存在")
        
        # 插入企业财务数据示例
        cursor.execute("SELECT COUNT(*) FROM company_financials")
        if cursor.fetchone()[0] == 0:
            print("插入企业财务数据示例...")
            
            financial_data = [
                (2024, 1, 5000000.00, ********.00, 1.2),
                (2024, 2, 4800000.00, ********.00, 1.15),
                (2024, 3, 5200000.00, ********.00, 1.25),
                (2024, 4, 4900000.00, 52500000.00, 1.18),
                (2024, 5, 5300000.00, 53000000.00, 1.22),
                (2024, 6, 5100000.00, 53500000.00, 1.20),
                (2024, 7, 5400000.00, 54000000.00, 1.24),
                (2024, 8, 5200000.00, 54500000.00, 1.21),
                (2024, 9, 5500000.00, 55000000.00, 1.26),
                (2024, 10, 5300000.00, 55500000.00, 1.23),
                (2024, 11, 5600000.00, 56000000.00, 1.27),
                (2024, 12, 5800000.00, 56500000.00, 1.30)
            ]
            
            insert_sql = """
            INSERT INTO company_financials (year, month, monthly_operating_cash_flow, total_assets, average_asset_turnover_rate) 
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.executemany(insert_sql, financial_data)
            print("✓ 企业财务数据插入完成")
        else:
            print("✓ 企业财务数据已存在")
        
        # 为现有固定资产更新金融字段的默认值
        print("更新现有固定资产的金融字段...")
        
        update_sql = """
        UPDATE materials 
        SET 
            useful_life_years = CASE 
                WHEN name LIKE '%电脑%' OR name LIKE '%笔记本%' THEN 3
                WHEN name LIKE '%打印机%' OR name LIKE '%复印机%' THEN 5
                WHEN name LIKE '%空调%' OR name LIKE '%投影仪%' THEN 8
                WHEN name LIKE '%办公桌%' OR name LIKE '%办公椅%' OR name LIKE '%文件柜%' THEN 10
                ELSE 5
            END,
            residual_value = price * 0.05,
            accumulated_depreciation = CASE 
                WHEN category = 'fixed_asset' THEN 
                    GREATEST(0, (price - price * 0.05) * DATEDIFF(CURDATE(), purchase_date) / 365 / useful_life_years)
                ELSE 0
            END
        WHERE category = 'fixed_asset' AND accumulated_depreciation = 0
        """
        cursor.execute(update_sql)
        print("✓ 固定资产金融字段更新完成")
        
        # 提交事务
        conn.commit()
        print("数据库升级完成！")
        
        # 验证升级结果
        cursor.execute("SHOW COLUMNS FROM materials")
        columns = cursor.fetchall()
        print(f"\nmaterials表现有字段数: {len(columns)}")
        
        cursor.execute("SELECT COUNT(*) FROM company_financials")
        financial_count = cursor.fetchone()[0]
        print(f"企业财务数据记录数: {financial_count}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"数据库升级失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == '__main__':
    upgrade_database()
