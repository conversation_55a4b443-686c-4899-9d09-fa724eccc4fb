#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.smart_recommendation_agent import SmartRecommendationAgent
import pymysql

def test_recommendation_generation():
    """测试推荐生成功能"""
    print("=== 测试推荐生成功能 ===")
    
    try:
        # 创建推荐Agent
        agent = SmartRecommendationAgent()
        
        # 测试为用户ID=2（zhangsan）生成推荐
        user_id = 2
        print(f"为用户ID {user_id} 生成推荐...")
        
        recommendations = agent.generate_user_recommendations(user_id)
        
        if recommendations:
            print(f"成功生成 {len(recommendations)} 条推荐:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec['material_name']} (评分: {rec['score']:.2f})")
                print(f"     原因: {rec['reason']}")
                print(f"     类型: {rec['type']}")
                print()
            
            # 保存推荐
            agent.save_recommendations(user_id, recommendations)
            print("推荐已保存到数据库")
            
        else:
            print("没有生成任何推荐")
            
        # 测试为管理员用户生成推荐
        admin_user_id = 1
        print(f"\n为管理员用户ID {admin_user_id} 生成推荐...")
        
        admin_recommendations = agent.generate_user_recommendations(admin_user_id)
        
        if admin_recommendations:
            print(f"成功生成 {len(admin_recommendations)} 条管理推荐:")
            for i, rec in enumerate(admin_recommendations, 1):
                print(f"  {i}. {rec['material_name']} (评分: {rec['score']:.2f})")
                print(f"     原因: {rec['reason']}")
                print(f"     类型: {rec['type']}")
                print()
            
            # 保存推荐
            agent.save_recommendations(admin_user_id, admin_recommendations)
            print("管理推荐已保存到数据库")
            
        else:
            print("没有生成任何管理推荐")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_saved_recommendations():
    """检查保存的推荐"""
    print("\n=== 检查保存的推荐 ===")
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='lax217652',
            database='goods'
        )
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查询最新的推荐记录
        cursor.execute("""
            SELECT ur.user_id, ur.reason, ur.score, ur.type, ur.created_at,
                   COALESCE(m.name, 'Management Task') as material_name
            FROM user_recommendations ur
            LEFT JOIN materials m ON ur.material_id = m.id
            ORDER BY ur.created_at DESC
            LIMIT 10
        """)
        
        recommendations = cursor.fetchall()
        
        if recommendations:
            print(f"数据库中最新的 {len(recommendations)} 条推荐:")
            for rec in recommendations:
                print(f"  用户ID: {rec['user_id']}, 物资: {rec['material_name']}")
                print(f"  评分: {rec['score']:.2f}, 类型: {rec['type']}")
                print(f"  时间: {rec['created_at']}")
                print()
        else:
            print("数据库中没有推荐记录")
            
        conn.close()
        
    except Exception as e:
        print(f"检查推荐失败: {e}")

if __name__ == '__main__':
    test_recommendation_generation()
    check_saved_recommendations()
