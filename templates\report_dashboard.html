{% extends "base.html" %}

{% block title %}统计报表 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">统计报表</h1>
        <p>查看物资统计信息和生成各类报表</p>
    </div>
    
    <!-- 统计概览 -->
    {% if statistics %}
    <div class="stats-grid">
        {% if statistics.material_stats %}
            {% for stat in statistics.material_stats %}
            <div class="stat-card">
                <div class="stat-number">{{ stat.total_count or 0 }}</div>
                <div class="stat-label">
                    {% if stat.category == 'fixed_asset' %}固定资产数量{% else %}耗材数量{% endif %}
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">¥{{ "%.2f"|format(stat.total_value or 0) }}</div>
                <div class="stat-label">
                    {% if stat.category == 'fixed_asset' %}固定资产价值{% else %}耗材价值{% endif %}
                </div>
            </div>
            {% endfor %}
        {% endif %}
        
        {% if statistics.total_value %}
        <div class="stat-card">
            <div class="stat-number">¥{{ "%.2f"|format(statistics.total_value) }}</div>
            <div class="stat-label">总价值</div>
        </div>
        {% endif %}
        
        {% if statistics.total_materials %}
        <div class="stat-card">
            <div class="stat-number">{{ statistics.total_materials }}</div>
            <div class="stat-label">总物资数量</div>
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- 分类统计图表 -->
    {% if category_stats %}
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">分类统计</h2>
        </div>
        
        <div id="statisticsChart"></div>
        
        <div class="stats-grid">
            {% for category, data in category_stats.items() %}
            <div class="stat-card">
                <div class="stat-number">{{ data.count }}</div>
                <div class="stat-label">
                    {% if category == 'fixed_asset' %}固定资产{% else %}耗材{% endif %}
                </div>
                <div style="font-size: 0.875rem; color: var(--dark-color); margin-top: 0.5rem;">
                    价值：¥{{ "%.2f"|format(data.value) }}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- 金融分析指标 -->
    {% if financial_stats %}
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">金融分析指标</h2>
            <p>企业资产管理的金融分析数据</p>
        </div>

        <!-- 固定资产净值分析 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <h4>固定资产净值分析</h4>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">¥{{ "%.2f"|format(financial_stats.fixed_asset_net_value_ratio.total_original_value or 0) }}</div>
                        <div class="stat-label">资产原值</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">¥{{ "%.2f"|format(financial_stats.fixed_asset_net_value_ratio.total_net_value or 0) }}</div>
                        <div class="stat-label">资产净值</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ "%.2f"|format(financial_stats.fixed_asset_net_value_ratio.net_value_ratio or 0) }}%</div>
                        <div class="stat-label">净值占比</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ "%.2f"|format(financial_stats.fixed_asset_net_value_ratio.depreciation_ratio or 0) }}%</div>
                        <div class="stat-label">折旧率</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 耗材采购现金流分析 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <h4>耗材采购现金流分析 ({{ financial_stats.consumable_cash_flow_ratio.current_year }}年{{ financial_stats.consumable_cash_flow_ratio.current_month }}月)</h4>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">¥{{ "%.2f"|format(financial_stats.consumable_cash_flow_ratio.monthly_operating_cash_flow or 0) }}</div>
                        <div class="stat-label">月度经营现金流</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">¥{{ "%.2f"|format(financial_stats.consumable_cash_flow_ratio.total_consumable_cost or 0) }}</div>
                        <div class="stat-label">耗材采购支出</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ "%.2f"|format(financial_stats.consumable_cash_flow_ratio.cash_flow_ratio or 0) }}%</div>
                        <div class="stat-label">现金流占比</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科室资产周转率分析 -->
        {% if financial_stats.department_asset_turnover.department_turnovers %}
        <div class="row mb-4">
            <div class="col-md-12">
                <h4>科室资产周转率分析</h4>
                <p><strong>企业平均周转率:</strong> {{ "%.4f"|format(financial_stats.department_asset_turnover.company_avg_turnover or 1.0) }}</p>

                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>科室名称</th>
                            <th>资产原值</th>
                            <th>使用次数</th>
                            <th>使用时长(小时)</th>
                            <th>周转率</th>
                            <th>对比企业平均</th>
                            <th>评价</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dept in financial_stats.department_asset_turnover.department_turnovers %}
                        <tr>
                            <td>{{ dept.department_name }}</td>
                            <td>¥{{ "%.2f"|format(dept.total_asset_value) }}</td>
                            <td>{{ dept.total_usage_count }}</td>
                            <td>{{ "%.2f"|format(dept.total_usage_hours) }}</td>
                            <td>{{ "%.4f"|format(dept.turnover_rate) }}</td>
                            <td>{{ "%.2f"|format(dept.vs_company_avg) }}%</td>
                            <td>
                                {% if dept.vs_company_avg >= 120 %}
                                    <span class="badge bg-success">优秀</span>
                                {% elif dept.vs_company_avg >= 100 %}
                                    <span class="badge bg-warning">良好</span>
                                {% elif dept.vs_company_avg >= 80 %}
                                    <span class="badge bg-info">一般</span>
                                {% else %}
                                    <span class="badge bg-danger">需改进</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- 报表生成 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">报表生成</h2>
        </div>

        <div class="d-flex gap-2 mb-3">
            <a href="{{ url_for('report.material_report') }}" class="btn btn-primary">物资台账报表</a>
            <a href="{{ url_for('report.department_report') }}" class="btn btn-info">科室物资报表</a>
            <a href="{{ url_for('report.financial_report') }}" class="btn btn-warning">金融分析报表</a>
        </div>
        
        <h3>导出功能</h3>
        <div class="d-flex gap-2">
            <a href="{{ url_for('report.export_material_inventory') }}" class="btn btn-success">
                导出物资台账 (Excel)
            </a>
            <a href="{{ url_for('report.export_material_inventory', category='fixed_asset') }}" class="btn btn-success">
                导出固定资产台账 (Excel)
            </a>
            <a href="{{ url_for('report.export_material_inventory', category='consumable') }}" class="btn btn-success">
                导出耗材台账 (Excel)
            </a>
            <a href="{{ url_for('report.export_allocation_report') }}" class="btn btn-warning">
                导出分配报表 (Excel)
            </a>
            <a href="{{ url_for('report.export_financial_report') }}" class="btn btn-danger">
                导出金融分析报表 (Excel)
            </a>
        </div>
    </div>
    
    <!-- 科室统计（仅管理员） -->
    {% if user.role == 'admin' and statistics.department_stats %}
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">科室统计</h2>
        </div>
        
        <table class="table">
            <thead>
                <tr>
                    <th>科室名称</th>
                    <th>用户数量</th>
                    <th>分配物资数量</th>
                </tr>
            </thead>
            <tbody>
                {% for dept in statistics.department_stats %}
                <tr>
                    <td>{{ dept.name }}</td>
                    <td>{{ dept.user_count or 0 }}</td>
                    <td>{{ dept.allocation_count or 0 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
</div>

<script>
// 初始化图表数据
document.addEventListener('DOMContentLoaded', function() {
    {% if category_stats %}
    const categoryStats = {{ category_stats | tojson }};
    if (categoryStats) {
        renderCategoryChart(categoryStats);
    }
    {% endif %}
});

function renderCategoryChart(data) {
    const chartContainer = document.getElementById('statisticsChart');
    if (!chartContainer) return;
    
    chartContainer.innerHTML = '';
    
    Object.keys(data).forEach((category, index) => {
        const item = data[category];
        const bar = document.createElement('div');
        bar.className = 'chart-bar';
        bar.style.cssText = `
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        `;
        
        const label = document.createElement('div');
        label.style.cssText = `
            width: 100px;
            text-align: right;
            margin-right: 1rem;
            font-size: 0.875rem;
        `;
        label.textContent = category === 'fixed_asset' ? '固定资产' : '耗材';
        
        const barContainer = document.createElement('div');
        barContainer.style.cssText = `
            flex: 1;
            height: 30px;
            background-color: #f0f0f0;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        `;
        
        const barFill = document.createElement('div');
        const maxValue = Math.max(...Object.values(data).map(d => d.count));
        const percentage = maxValue > 0 ? (item.count / maxValue) * 100 : 0;
        barFill.style.cssText = `
            height: 100%;
            width: ${percentage}%;
            background: linear-gradient(135deg, ${category === 'fixed_asset' ? '#FFB6C1' : '#98FB98'}, ${category === 'fixed_asset' ? '#E6E6FA' : '#90EE90'});
            border-radius: 15px;
            transition: width 0.5s ease;
        `;
        
        const valueLabel = document.createElement('div');
        valueLabel.style.cssText = `
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.875rem;
            font-weight: bold;
        `;
        valueLabel.textContent = item.count;
        
        barContainer.appendChild(barFill);
        barContainer.appendChild(valueLabel);
        bar.appendChild(label);
        bar.appendChild(barContainer);
        chartContainer.appendChild(bar);
    });
}
</script>
{% endblock %}
