#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

def create_agent_tables():
    """创建Agent相关的数据库表"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='qyf20031211',
            database='goods'
        )
        cursor = conn.cursor()
        
        print("=== 创建Agent相关表 ===")
        
        # 创建用户推荐表
        print("创建user_recommendations表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS user_recommendations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            material_id INT NOT NULL,
            score DECIMAL(3,2) NOT NULL DEFAULT 0.50,
            reason TEXT,
            type ENUM('department', 'global', 'personal') DEFAULT 'global',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_material_id (material_id),
            INDEX idx_score (score),
            INDEX idx_created_at (created_at)
        )
        """)
        print("✅ user_recommendations表创建成功")
        
        # 创建通知表
        print("创建notifications表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('user', 'admin', 'system') DEFAULT 'user',
            data TEXT,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_is_read (is_read),
            INDEX idx_created_at (created_at)
        )
        """)
        print("✅ notifications表创建成功")
        
        # 创建Agent执行历史表
        print("创建agent_execution_history表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS agent_execution_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            agent_name VARCHAR(100) NOT NULL,
            execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('success', 'failed', 'running') DEFAULT 'running',
            result_data TEXT,
            error_message TEXT,
            execution_duration INT DEFAULT 0,
            INDEX idx_agent_name (agent_name),
            INDEX idx_execution_time (execution_time),
            INDEX idx_status (status)
        )
        """)
        print("✅ agent_execution_history表创建成功")
        
        conn.commit()
        print("\n=== 所有表创建完成 ===")
        
        # 验证表是否创建成功
        cursor.execute('SHOW TABLES')
        tables = cursor.fetchall()
        table_names = [t[0] for t in tables]
        print("当前所有表:", table_names)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")

if __name__ == '__main__':
    create_agent_tables()
