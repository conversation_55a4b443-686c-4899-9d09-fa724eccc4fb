{% extends "base.html" %}

{% block title %}金融分析报表 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">金融分析报表</h1>
        <p>企业物资管理的金融分析与评估</p>
    </div>
    
    {% if financial_stats %}
    <!-- 固定资产净值分析 -->
    <div class="card mb-4">
        <div class="card-header">
            <h2 class="card-title">固定资产净值分析</h2>
        </div>
        <div class="card-body">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">¥{{ "%.2f"|format(financial_stats.fixed_asset_net_value_ratio.total_original_value or 0) }}</div>
                    <div class="stat-label">资产原值</div>
                    <div class="stat-description">固定资产购置时的原始价值</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥{{ "%.2f"|format(financial_stats.fixed_asset_net_value_ratio.total_accumulated_depreciation or 0) }}</div>
                    <div class="stat-label">累计折旧</div>
                    <div class="stat-description">已计提的折旧总额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥{{ "%.2f"|format(financial_stats.fixed_asset_net_value_ratio.total_net_value or 0) }}</div>
                    <div class="stat-label">资产净值</div>
                    <div class="stat-description">扣除累计折旧后的净值</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ "%.2f"|format(financial_stats.fixed_asset_net_value_ratio.net_value_ratio or 0) }}%</div>
                    <div class="stat-label">净值占比</div>
                    <div class="stat-description">净值占原值的比例</div>
                </div>
            </div>
            
            <!-- 净值占比图表 -->
            <div class="mt-4">
                <canvas id="netValueChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 耗材采购现金流分析 -->
    <div class="card mb-4">
        <div class="card-header">
            <h2 class="card-title">耗材采购现金流分析</h2>
            <p class="text-muted">{{ financial_stats.consumable_cash_flow_ratio.current_year }}年{{ financial_stats.consumable_cash_flow_ratio.current_month }}月数据</p>
        </div>
        <div class="card-body">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">¥{{ "%.2f"|format(financial_stats.consumable_cash_flow_ratio.monthly_operating_cash_flow or 0) }}</div>
                    <div class="stat-label">月度经营现金流</div>
                    <div class="stat-description">企业当月经营活动产生的现金流</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥{{ "%.2f"|format(financial_stats.consumable_cash_flow_ratio.total_consumable_cost or 0) }}</div>
                    <div class="stat-label">耗材采购支出</div>
                    <div class="stat-description">当月耗材采购的现金支出</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ "%.2f"|format(financial_stats.consumable_cash_flow_ratio.cash_flow_ratio or 0) }}%</div>
                    <div class="stat-label">现金流占比</div>
                    <div class="stat-description">耗材支出占经营现金流的比例</div>
                </div>
            </div>
            
            <!-- 现金流占比图表 -->
            <div class="mt-4">
                <canvas id="cashFlowChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 科室资产周转率分析 -->
    {% if financial_stats.department_asset_turnover.department_turnovers %}
    <div class="card mb-4">
        <div class="card-header">
            <h2 class="card-title">科室资产周转率分析</h2>
            <p class="text-muted">企业平均周转率: {{ "%.4f"|format(financial_stats.department_asset_turnover.company_avg_turnover or 1.0) }}</p>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>科室名称</th>
                            <th>资产原值</th>
                            <th>使用次数</th>
                            <th>使用时长(小时)</th>
                            <th>周转率</th>
                            <th>对比企业平均</th>
                            <th>评价</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dept in financial_stats.department_asset_turnover.department_turnovers %}
                        <tr>
                            <td><strong>{{ dept.department_name }}</strong></td>
                            <td>¥{{ "%.2f"|format(dept.total_asset_value) }}</td>
                            <td>{{ dept.total_usage_count }}</td>
                            <td>{{ "%.2f"|format(dept.total_usage_hours) }}</td>
                            <td>{{ "%.4f"|format(dept.turnover_rate) }}</td>
                            <td>{{ "%.2f"|format(dept.vs_company_avg) }}%</td>
                            <td>
                                {% if dept.vs_company_avg >= 120 %}
                                    <span class="badge bg-success">优秀</span>
                                {% elif dept.vs_company_avg >= 100 %}
                                    <span class="badge bg-warning text-dark">良好</span>
                                {% elif dept.vs_company_avg >= 80 %}
                                    <span class="badge bg-info">一般</span>
                                {% else %}
                                    <span class="badge bg-danger">需改进</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 周转率对比图表 -->
            <div class="mt-4">
                <canvas id="turnoverChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- 导出功能 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">导出功能</h2>
        </div>
        <div class="card-body">
            <div class="d-flex gap-2">
                <a href="{{ url_for('report.export_financial_report') }}" class="btn btn-success">
                    <i class="fas fa-download"></i> 导出金融分析报表 (Excel)
                </a>
                <a href="{{ url_for('report.report_dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回报表仪表板
                </a>
            </div>
        </div>
    </div>
    
    {% else %}
    <div class="alert alert-warning">
        <h4>暂无金融分析数据</h4>
        <p>请确保系统中有相关的物资和财务数据。</p>
    </div>
    {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if financial_stats %}
    
    // 固定资产净值占比图表
    const netValueCtx = document.getElementById('netValueChart').getContext('2d');
    new Chart(netValueCtx, {
        type: 'doughnut',
        data: {
            labels: ['资产净值', '累计折旧'],
            datasets: [{
                data: [
                    {{ financial_stats.fixed_asset_net_value_ratio.total_net_value or 0 }},
                    {{ financial_stats.fixed_asset_net_value_ratio.total_accumulated_depreciation or 0 }}
                ],
                backgroundColor: ['#28a745', '#dc3545'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '固定资产净值构成'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 现金流占比图表
    const cashFlowCtx = document.getElementById('cashFlowChart').getContext('2d');
    const cashFlowRatio = {{ financial_stats.consumable_cash_flow_ratio.cash_flow_ratio or 0 }};
    new Chart(cashFlowCtx, {
        type: 'doughnut',
        data: {
            labels: ['耗材采购支出', '其他经营现金流'],
            datasets: [{
                data: [
                    cashFlowRatio,
                    100 - cashFlowRatio
                ],
                backgroundColor: ['#ffc107', '#6c757d'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '耗材采购现金流占比'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    {% if financial_stats.department_asset_turnover.department_turnovers %}
    // 科室周转率对比图表
    const turnoverCtx = document.getElementById('turnoverChart').getContext('2d');
    const departmentNames = [
        {% for dept in financial_stats.department_asset_turnover.department_turnovers %}
        '{{ dept.department_name }}',
        {% endfor %}
    ];
    const turnoverRates = [
        {% for dept in financial_stats.department_asset_turnover.department_turnovers %}
        {{ dept.vs_company_avg }},
        {% endfor %}
    ];
    
    new Chart(turnoverCtx, {
        type: 'bar',
        data: {
            labels: departmentNames,
            datasets: [{
                label: '对比企业平均(%)',
                data: turnoverRates,
                backgroundColor: turnoverRates.map(rate => {
                    if (rate >= 120) return '#28a745';
                    if (rate >= 100) return '#ffc107';
                    if (rate >= 80) return '#17a2b8';
                    return '#dc3545';
                }),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '科室资产周转率对比企业平均'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '对比企业平均(%)'
                    }
                }
            }
        }
    });
    {% endif %}
    
    {% endif %}
});
</script>
{% endblock %}
