{% extends "base.html" %}

{% block title %}编辑用户{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">编辑用户</h1>
        <p>修改用户 "{{ user.real_name }}" 的信息</p>
    </div>
    
    <div class="card-body">
        <form method="POST" id="editUserForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" id="username" class="form-control" value="{{ user.username }}" disabled>
                        <small class="form-text">用户名不可修改</small>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="real_name" class="form-label">真实姓名 <span class="required">*</span></label>
                        <input type="text" id="real_name" name="real_name" class="form-control" value="{{ user.real_name }}" required>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="role" class="form-label">角色 <span class="required">*</span></label>
                        <select id="role" name="role" class="form-control" required>
                            <option value="employee" {% if user.role == 'employee' %}selected{% endif %}>员工</option>
                            <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>管理员</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="department_id" class="form-label">科室</label>
                        <select id="department_id" name="department_id" class="form-control">
                            <option value="">请选择科室</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if user.department_id == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" id="email" name="email" class="form-control" value="{{ user.email or '' }}">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="phone" class="form-label">电话</label>
                        <input type="tel" id="phone" name="phone" class="form-control" value="{{ user.phone or '' }}">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="status" class="form-label">状态</label>
                        <select id="status" name="status" class="form-control">
                            <option value="active" {% if user.status == 'active' %}selected{% endif %}>活跃</option>
                            <option value="inactive" {% if user.status == 'inactive' %}selected{% endif %}>禁用</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">更新用户</button>
                <a href="{{ url_for('user.user_list') }}" class="btn btn-secondary">取消</a>
                <a href="{{ url_for('user.user_detail', user_id=user.id) }}" class="btn btn-info">查看详情</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    // 验证真实姓名
    var realName = document.getElementById('real_name').value;
    if (realName.trim().length < 2) {
        e.preventDefault();
        alert('请输入有效的真实姓名！');
        return false;
    }
});
</script>
{% endblock %}
