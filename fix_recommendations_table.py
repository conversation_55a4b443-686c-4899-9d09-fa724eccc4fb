#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复user_recommendations表结构，允许material_id为NULL
"""

import pymysql

def fix_recommendations_table():
    """修复推荐表结构"""
    try:
        # 连接数据库
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='lax217652',
            database='goods',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        cursor = conn.cursor()
        
        print("=== 修复user_recommendations表结构 ===")
        
        # 修改material_id字段，允许为NULL
        print("修改material_id字段，允许为NULL...")
        cursor.execute("""
            ALTER TABLE user_recommendations 
            MODIFY COLUMN material_id INT NULL
        """)
        
        # 删除外键约束（如果存在）
        print("检查并删除外键约束...")
        cursor.execute("""
            SELECT CONSTRAINT_NAME 
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'goods' 
            AND TABLE_NAME = 'user_recommendations' 
            AND COLUMN_NAME = 'material_id'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        """)
        
        constraints = cursor.fetchall()
        for constraint in constraints:
            constraint_name = constraint['CONSTRAINT_NAME']
            print(f"删除外键约束: {constraint_name}")
            cursor.execute(f"ALTER TABLE user_recommendations DROP FOREIGN KEY {constraint_name}")
        
        # 重新添加外键约束，允许NULL
        print("重新添加外键约束（允许NULL）...")
        cursor.execute("""
            ALTER TABLE user_recommendations 
            ADD CONSTRAINT fk_recommendations_material 
            FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE
        """)
        
        conn.commit()
        print("✅ 表结构修复完成")
        
        # 验证修改
        cursor.execute("DESCRIBE user_recommendations")
        columns = cursor.fetchall()
        
        print("\n当前表结构:")
        for col in columns:
            print(f"  {col['Field']}: {col['Type']} {'NULL' if col['Null'] == 'YES' else 'NOT NULL'}")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    fix_recommendations_table()
