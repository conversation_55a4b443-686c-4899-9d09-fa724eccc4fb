#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("开始测试推荐系统...")

try:
    # 测试数据库连接
    print("1. 测试数据库连接...")
    from dao.database import Database
    db = Database()
    result = db.execute_query("SELECT COUNT(*) as count FROM users")
    print(f"   用户总数: {result[0]['count']}")
    
    # 测试MaterialDAO
    print("2. 测试MaterialDAO...")
    from dao.material_dao import MaterialDAO
    material_dao = MaterialDAO()
    materials = material_dao.db.execute_query("SELECT COUNT(*) as count FROM materials")
    print(f"   物资总数: {materials[0]['count']}")
    
    # 测试推荐Agent
    print("3. 测试推荐Agent...")
    from agents.smart_recommendation_agent import SmartRecommendationAgent
    agent = SmartRecommendationAgent()
    print(f"   Agent名称: {agent.name}")
    
    # 测试获取用户信息
    print("4. 测试获取用户信息...")
    user_id = 2
    user_info = agent.get_user_info(user_id)
    print(f"   用户信息: {user_info}")
    
    # 测试生成推荐
    print("5. 测试生成推荐...")
    recommendations = agent.generate_user_recommendations(user_id)
    print(f"   生成推荐数量: {len(recommendations) if recommendations else 0}")
    
    if recommendations:
        for i, rec in enumerate(recommendations[:3], 1):
            print(f"   {i}. {rec.get('material_name', 'N/A')} (评分: {rec.get('score', 0):.2f})")
    
    print("✅ 测试完成！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
