from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO
from dao.department_dao import DepartmentDAO
from dao.user_dao import UserDAO
from dao.database import db
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from datetime import datetime, date
import io
import calendar

class ReportService:
    def __init__(self):
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()
        self.department_dao = DepartmentDAO()
        self.user_dao = UserDAO()
    
    def get_material_statistics(self, user):
        """获取物资统计信息"""
        if user.is_admin():
            # 管理员可以查看全部统计
            stats = self.material_dao.get_material_statistics()
            dept_stats = self.department_dao.get_department_statistics()
            
            return {
                'material_stats': stats,
                'department_stats': dept_stats,
                'total_value': sum(item['total_value'] or 0 for item in stats),
                'total_materials': sum(item['total_count'] or 0 for item in stats)
            }
        else:
            # 普通员工只能查看本科室统计
            return self._get_department_statistics(user.department_id)
    
    def get_category_statistics(self, user):
        """获取分类统计"""
        if user.is_admin():
            # 管理员查看全部统计
            stats = self.material_dao.get_material_statistics()
        else:
            # 普通员工只查看本科室的统计
            stats = self._get_department_material_statistics(user.department_id)

        result = {
            'fixed_asset': {'count': 0, 'value': 0},
            'consumable': {'count': 0, 'value': 0}
        }

        for stat in stats:
            category = stat['category']
            if category in result:
                result[category]['count'] = stat['total_count'] or 0
                result[category]['value'] = stat['total_value'] or 0

        return result
    
    def get_department_material_report(self, department_id, user):
        """获取科室物资报表"""
        if not user.is_admin() and user.department_id != department_id:
            raise PermissionError("无权查看其他科室数据")

        allocations = self.allocation_dao.get_allocations_by_department(department_id)
        department = self.department_dao.get_department_by_id(department_id)

        # 计算汇总信息
        summary = self._calculate_department_summary(allocations)

        # 获取科室用户数量
        users = self.user_dao.get_users_by_department(department_id)

        # 为每个分配记录添加 total_value 字段
        enhanced_allocations = []
        for allocation in allocations:
            allocation_dict = dict(allocation) if isinstance(allocation, dict) else allocation
            unit_price = allocation_dict.get('unit_price', 0) or 0
            quantity = allocation_dict.get('quantity', 1) or 1
            allocation_dict['total_value'] = float(unit_price) * float(quantity)
            enhanced_allocations.append(allocation_dict)

        return {
            'department': department.to_dict() if department else None,
            'department_name': department.name if department else '未知科室',
            'total_users': len(users),
            'total_materials': len(allocations),
            'total_value': summary['total_value'],
            'allocations': enhanced_allocations,
            'summary': summary
        }
    
    def generate_material_inventory_excel(self, user, category=None):
        """生成物资台账Excel报表"""
        if category:
            materials = self.material_dao.get_materials_by_category(category)
        else:
            materials = self.material_dao.get_all_materials()
        
        # 如果是普通员工，过滤数据
        if not user.is_admin():
            dept_materials = self.allocation_dao.get_allocations_by_department(user.department_id)
            dept_material_ids = {item['material_id'] for item in dept_materials}
            materials = [m for m in materials if m.id in dept_material_ids]
        
        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "物资台账"
        
        # 设置标题
        title = f"物资台账报表 - {category if category else '全部'}"
        ws.merge_cells('A1:J1')
        ws['A1'] = title
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center')
        
        # 设置表头
        headers = ['序号', '资产编号', '物资名称', '型号', '类别', '单价', '数量', '剩余数量', '状态', '购入日期']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 填充数据
        for row, material in enumerate(materials, 4):
            ws.cell(row=row, column=1, value=row-3)
            ws.cell(row=row, column=2, value=material.asset_number or '-')
            ws.cell(row=row, column=3, value=material.name)
            ws.cell(row=row, column=4, value=material.model or '-')
            ws.cell(row=row, column=5, value='固定资产' if material.category == 'fixed_asset' else '耗材')
            ws.cell(row=row, column=6, value=material.price)
            ws.cell(row=row, column=7, value=material.quantity)
            ws.cell(row=row, column=8, value=material.remaining_quantity)
            ws.cell(row=row, column=9, value=self._get_status_text(material.status))
            ws.cell(row=row, column=10, value=material.purchase_date.strftime('%Y-%m-%d') if material.purchase_date else '-')
        
        # 调整列宽
        for col in range(1, 11):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15
        
        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        return output

    def _get_department_material_statistics(self, department_id):
        """获取科室物资统计"""
        try:
            # 获取科室分配的固定资产统计
            fixed_asset_sql = """
            SELECT m.category,
                   COUNT(DISTINCT m.id) as total_count,
                   SUM(m.price * ma.quantity) as total_value
            FROM materials m
            JOIN material_allocations ma ON m.id = ma.material_id
            WHERE ma.department_id = %s
            AND m.status = 'available'
            AND m.category = 'fixed_asset'
            GROUP BY m.category
            """

            # 获取耗材统计（只统计该科室实际申请过的耗材）
            consumable_sql = """
            SELECT 'consumable' as category,
                   COUNT(DISTINCT m.id) as total_count,
                   SUM(m.price * mr.quantity) as total_value
            FROM materials m
            INNER JOIN material_requests mr ON m.id = mr.material_id
            WHERE m.category = 'consumable'
            AND m.status = 'available'
            AND mr.user_id IN (
                SELECT id FROM users WHERE department_id = %s
            )
            AND mr.status = 'approved'
            """

            # 执行查询并合并结果
            fixed_assets = self.material_dao.db.execute_query(fixed_asset_sql, (department_id,))
            consumables = self.material_dao.db.execute_query(consumable_sql, (department_id,))

            # 合并结果
            result = []
            result.extend(fixed_assets)
            result.extend(consumables)

            return result
        except Exception as e:
            print(f"获取科室物资统计失败: {e}")
            return []

    def generate_department_report_excel(self, user, department_id):
        """生成科室物资报表Excel"""
        if not user.is_admin() and user.department_id != department_id:
            raise PermissionError("无权查看其他科室数据")

        # 获取科室报表数据
        report_data = self.get_department_material_report(department_id, user)

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "科室物资报表"

        # 设置标题
        dept_name = report_data.get('department_name', '未知科室')
        ws['A1'] = f"{dept_name} - 物资分配报表"
        ws.merge_cells('A1:H1')

        # 设置标题样式
        title_font = Font(size=16, bold=True)
        title_alignment = Alignment(horizontal='center', vertical='center')
        ws['A1'].font = title_font
        ws['A1'].alignment = title_alignment

        # 添加汇总信息
        row = 3
        ws[f'A{row}'] = f"科室名称: {dept_name}"
        ws[f'D{row}'] = f"科室人数: {report_data.get('total_users', 0)}"
        row += 1
        ws[f'A{row}'] = f"分配物资数: {report_data.get('total_materials', 0)}"
        ws[f'D{row}'] = f"物资总价值: ¥{report_data.get('total_value', 0):.2f}"

        # 设置表头
        row = 6
        headers = ['物资名称', '分类', '分配用户', '数量', '单价', '总价值', '分配日期', '状态']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

        # 填充数据
        row += 1
        for allocation in report_data.get('allocations', []):
            ws.cell(row=row, column=1, value=allocation.get('material_name', ''))
            ws.cell(row=row, column=2, value='固定资产' if allocation.get('category') == 'fixed_asset' else '消耗品')
            ws.cell(row=row, column=3, value=allocation.get('user_name', '科室公用'))
            ws.cell(row=row, column=4, value=allocation.get('quantity', 1))
            ws.cell(row=row, column=5, value=f"¥{allocation.get('unit_price', 0):.2f}")
            ws.cell(row=row, column=6, value=f"¥{allocation.get('total_value', 0):.2f}")

            # 处理分配日期
            alloc_date = allocation.get('allocation_date') or allocation.get('allocated_at')
            if alloc_date:
                if hasattr(alloc_date, 'strftime'):
                    ws.cell(row=row, column=7, value=alloc_date.strftime('%Y-%m-%d'))
                else:
                    ws.cell(row=row, column=7, value=str(alloc_date))
            else:
                ws.cell(row=row, column=7, value='')

            # 状态
            status = allocation.get('status', 'active')
            status_text = '正常' if status == 'active' else ('已归还' if status == 'returned' else '其他')
            ws.cell(row=row, column=8, value=status_text)

            row += 1

        # 调整列宽
        for col in range(1, 9):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return output

    def generate_allocation_report_excel(self, user, department_id=None):
        """生成分配报表Excel"""
        if department_id:
            if not user.is_admin() and user.department_id != department_id:
                raise PermissionError("无权查看其他科室数据")
            allocations = self.allocation_dao.get_allocations_by_department(department_id)
            title = f"科室物资分配报表"
        else:
            if not user.is_admin():
                allocations = self.allocation_dao.get_allocations_by_user(user.id)
                title = "个人物资分配报表"
            else:
                allocations = self.allocation_dao.get_all_allocations()
                title = "全部物资分配报表"
        
        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "分配报表"
        
        # 设置标题
        ws.merge_cells('A1:I1')
        ws['A1'] = title
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center')
        
        # 设置表头
        headers = ['序号', '物资名称', '类别', '科室', '领取人', '数量', '分配日期', '状态', '备注']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 填充数据
        for row, allocation in enumerate(allocations, 4):
            ws.cell(row=row, column=1, value=row-3)
            ws.cell(row=row, column=2, value=allocation.get('material_name', '-'))
            ws.cell(row=row, column=3, value='固定资产' if allocation.get('category') == 'fixed_asset' else '耗材')
            ws.cell(row=row, column=4, value=allocation.get('department_name', '-'))
            ws.cell(row=row, column=5, value=allocation.get('user_name', '-'))
            ws.cell(row=row, column=6, value=allocation.get('quantity', 0))
            ws.cell(row=row, column=7, value=allocation.get('allocation_date', '').strftime('%Y-%m-%d') if allocation.get('allocation_date') else '-')
            ws.cell(row=row, column=8, value=self._get_allocation_status_text(allocation.get('status')))
            ws.cell(row=row, column=9, value=allocation.get('notes', '-'))
        
        # 调整列宽
        for col in range(1, 10):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15
        
        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        return output
    
    def _get_department_statistics(self, department_id):
        """获取科室统计信息"""
        allocations = self.allocation_dao.get_allocations_by_department(department_id)
        
        stats = {
            'total_allocations': len(allocations),
            'fixed_asset_count': 0,
            'consumable_count': 0,
            'total_value': 0
        }
        
        for allocation in allocations:
            if allocation.get('category') == 'fixed_asset':
                stats['fixed_asset_count'] += 1
            else:
                stats['consumable_count'] += 1
        
        return stats
    
    def _calculate_department_summary(self, allocations):
        """计算科室汇总信息"""
        summary = {
            'total_items': len(allocations),
            'fixed_assets': 0,
            'consumables': 0,
            'active_allocations': 0,
            'total_value': 0.0
        }

        for allocation in allocations:
            if allocation.get('category') == 'fixed_asset':
                summary['fixed_assets'] += 1
            else:
                summary['consumables'] += 1

            if allocation.get('status') == 'allocated':
                summary['active_allocations'] += 1

            # 计算总价值
            unit_price = allocation.get('unit_price', 0) or 0
            quantity = allocation.get('quantity', 1) or 1
            summary['total_value'] += float(unit_price) * float(quantity)

        return summary
    
    def _get_status_text(self, status):
        """获取状态文本"""
        status_map = {
            'available': '可用',
            'in_use': '在用',
            'scrapped': '已报废'
        }
        return status_map.get(status, status)
    
    def _get_allocation_status_text(self, status):
        """获取分配状态文本"""
        status_map = {
            'allocated': '已分配',
            'returned': '已归还',
            'consumed': '已消耗'
        }
        return status_map.get(status, status)

    def get_advanced_statistics(self, user, category=None, department_id=None, start_date=None, end_date=None):
        """获取高级统计数据"""
        # 基础物资统计 - 根据用户权限过滤
        if user.is_admin():
            material_stats = self.material_dao.get_material_statistics()
        else:
            material_stats = self._get_department_material_statistics(user.department_id)

        # 按科室统计分配情况
        department_allocations = {}
        if user.is_admin():
            departments = self.department_dao.get_all_departments()
            for dept in departments:
                # 处理Department对象
                dept_id = dept.id if hasattr(dept, 'id') else dept['id']
                dept_name = dept.name if hasattr(dept, 'name') else dept['name']

                if department_id and dept_id != department_id:
                    continue

                allocations = self.allocation_dao.get_allocations_by_department(dept_id)

                # 按类别过滤
                if category:
                    allocations = [a for a in allocations if a.get('category') == category]

                # 按日期过滤
                if start_date or end_date:
                    filtered_allocations = []
                    for allocation in allocations:
                        alloc_date = allocation.get('allocated_date')
                        if alloc_date:
                            if isinstance(alloc_date, str):
                                from datetime import datetime
                                alloc_date = datetime.strptime(alloc_date, '%Y-%m-%d').date()

                            if start_date and alloc_date < datetime.strptime(start_date, '%Y-%m-%d').date():
                                continue
                            if end_date and alloc_date > datetime.strptime(end_date, '%Y-%m-%d').date():
                                continue
                        filtered_allocations.append(allocation)
                    allocations = filtered_allocations

                department_allocations[dept_name] = {
                    'total_allocations': len(allocations),
                    'fixed_assets': len([a for a in allocations if a.get('category') == 'fixed_asset']),
                    'consumables': len([a for a in allocations if a.get('category') == 'consumable']),
                    'allocations': allocations[:10]  # 最近10条记录
                }
        else:
            # 普通员工只能查看自己科室的统计
            dept = self.department_dao.get_department_by_id(user.department_id)
            if dept:
                dept_name = dept.name if hasattr(dept, 'name') else dept['name']
                allocations = self.allocation_dao.get_allocations_by_department(user.department_id)

                # 按类别过滤
                if category:
                    allocations = [a for a in allocations if a.get('category') == category]

                # 按日期过滤
                if start_date or end_date:
                    filtered_allocations = []
                    for allocation in allocations:
                        alloc_date = allocation.get('allocated_date')
                        if alloc_date:
                            if isinstance(alloc_date, str):
                                from datetime import datetime
                                alloc_date = datetime.strptime(alloc_date, '%Y-%m-%d').date()

                            if start_date and alloc_date < datetime.strptime(start_date, '%Y-%m-%d').date():
                                continue
                            if end_date and alloc_date > datetime.strptime(end_date, '%Y-%m-%d').date():
                                continue
                        filtered_allocations.append(allocation)
                    allocations = filtered_allocations

                department_allocations[dept_name] = {
                    'total_allocations': len(allocations),
                    'fixed_assets': len([a for a in allocations if a.get('category') == 'fixed_asset']),
                    'consumables': len([a for a in allocations if a.get('category') == 'consumable']),
                    'allocations': allocations[:10]  # 最近10条记录
                }

        # 按类别统计
        category_summary = {
            'fixed_asset': {'count': 0, 'value': 0, 'allocated': 0},
            'consumable': {'count': 0, 'value': 0, 'allocated': 0}
        }

        for stat in material_stats:
            cat = stat['category']
            if cat in category_summary:
                category_summary[cat]['count'] = stat['total_count'] or 0
                category_summary[cat]['value'] = float(stat['total_value'] or 0)

        # 统计已分配数量 - 根据用户权限过滤
        if user.is_admin():
            all_allocations = self.allocation_dao.get_all_allocations()
        else:
            all_allocations = self.allocation_dao.get_allocations_by_department(user.department_id)

        for allocation in all_allocations:
            cat = allocation.get('category')
            if cat in category_summary:
                category_summary[cat]['allocated'] += 1

        return {
            'material_stats': material_stats,
            'department_allocations': department_allocations,
            'category_summary': category_summary,
            'total_departments': len(department_allocations),
            'filters_applied': bool(category or department_id or start_date or end_date)
        }

    def get_financial_statistics(self, user):
        """获取金融统计数据"""
        try:
            # 固定资产净值占比计算
            fixed_asset_net_value_ratio = self._calculate_fixed_asset_net_value_ratio(user)

            # 耗材采购现金流占比计算
            consumable_cash_flow_ratio = self._calculate_consumable_cash_flow_ratio(user)

            # 科室资产周转率计算
            department_asset_turnover = self._calculate_department_asset_turnover(user)

            return {
                'fixed_asset_net_value_ratio': fixed_asset_net_value_ratio,
                'consumable_cash_flow_ratio': consumable_cash_flow_ratio,
                'department_asset_turnover': department_asset_turnover
            }
        except Exception as e:
            print(f"获取金融统计数据失败: {e}")
            return {
                'fixed_asset_net_value_ratio': {},
                'consumable_cash_flow_ratio': {},
                'department_asset_turnover': {}
            }

    def _calculate_fixed_asset_net_value_ratio(self, user):
        """计算固定资产净值占比（扣除累计折旧后的净值）"""
        try:
            # 获取固定资产数据
            if user.is_admin():
                sql = """
                SELECT
                    SUM(price * quantity) as total_original_value,
                    SUM(accumulated_depreciation * quantity) as total_accumulated_depreciation,
                    SUM((price - accumulated_depreciation) * quantity) as total_net_value
                FROM materials
                WHERE category = 'fixed_asset' AND status != 'scrapped'
                """
                params = ()
            else:
                sql = """
                SELECT
                    SUM(m.price * ma.quantity) as total_original_value,
                    SUM(m.accumulated_depreciation * ma.quantity) as total_accumulated_depreciation,
                    SUM((m.price - m.accumulated_depreciation) * ma.quantity) as total_net_value
                FROM materials m
                JOIN material_allocations ma ON m.id = ma.material_id
                WHERE m.category = 'fixed_asset' AND m.status != 'scrapped'
                AND ma.department_id = %s AND ma.status = 'allocated'
                """
                params = (user.department_id,)

            result = db.execute_query_one(sql, params)

            if result and result['total_original_value']:
                original_value = float(result['total_original_value'] or 0)
                accumulated_depreciation = float(result['total_accumulated_depreciation'] or 0)
                net_value = float(result['total_net_value'] or 0)

                # 计算净值占比
                net_value_ratio = (net_value / original_value * 100) if original_value > 0 else 0
                depreciation_ratio = (accumulated_depreciation / original_value * 100) if original_value > 0 else 0

                return {
                    'total_original_value': original_value,
                    'total_accumulated_depreciation': accumulated_depreciation,
                    'total_net_value': net_value,
                    'net_value_ratio': round(net_value_ratio, 2),
                    'depreciation_ratio': round(depreciation_ratio, 2)
                }
            else:
                return {
                    'total_original_value': 0,
                    'total_accumulated_depreciation': 0,
                    'total_net_value': 0,
                    'net_value_ratio': 0,
                    'depreciation_ratio': 0
                }
        except Exception as e:
            print(f"计算固定资产净值占比失败: {e}")
            return {
                'total_original_value': 0,
                'total_accumulated_depreciation': 0,
                'total_net_value': 0,
                'net_value_ratio': 0,
                'depreciation_ratio': 0
            }

    def _calculate_consumable_cash_flow_ratio(self, user):
        """计算耗材采购现金流占比（对比企业月度经营现金流）"""
        try:
            # 获取当前年月
            current_date = date.today()
            current_year = current_date.year
            current_month = current_date.month

            # 设置默认的企业月度经营现金流（如果数据库中没有数据）
            monthly_operating_cash_flow = 3500000.00  # 默认350万

            # 尝试从数据库获取企业月度经营现金流
            try:
                company_cash_flow_sql = """
                SELECT monthly_operating_cash_flow
                FROM company_financials
                WHERE year = %s AND month = %s
                """
                company_result = db.execute_query_one(company_cash_flow_sql, (current_year, current_month))

                if not company_result:
                    # 如果没有当月数据，取最近一个月的数据
                    company_cash_flow_sql = """
                    SELECT monthly_operating_cash_flow
                    FROM company_financials
                    ORDER BY year DESC, month DESC
                    LIMIT 1
                    """
                    company_result = db.execute_query_one(company_cash_flow_sql)

                if company_result and company_result['monthly_operating_cash_flow']:
                    monthly_operating_cash_flow = float(company_result['monthly_operating_cash_flow'])
            except Exception as e:
                print(f"获取企业现金流数据失败，使用默认值: {e}")

            # 获取耗材采购金额（基于现有的耗材分配记录来模拟）
            total_consumable_cost = 0
            try:
                if user.is_admin():
                    # 管理员查看所有耗材的分配成本
                    consumable_sql = """
                    SELECT SUM(m.price * ma.quantity) as total_consumable_cost
                    FROM materials m
                    JOIN material_allocations ma ON m.id = ma.material_id
                    WHERE m.category = 'consumable'
                    AND ma.status = 'consumed'
                    AND YEAR(ma.allocation_date) = %s
                    AND MONTH(ma.allocation_date) = %s
                    """
                    params = (current_year, current_month)
                else:
                    # 普通员工查看本科室的耗材成本
                    consumable_sql = """
                    SELECT SUM(m.price * ma.quantity) as total_consumable_cost
                    FROM materials m
                    JOIN material_allocations ma ON m.id = ma.material_id
                    WHERE m.category = 'consumable'
                    AND ma.status = 'consumed'
                    AND ma.department_id = %s
                    AND YEAR(ma.allocation_date) = %s
                    AND MONTH(ma.allocation_date) = %s
                    """
                    params = (user.department_id, current_year, current_month)

                consumable_result = db.execute_query_one(consumable_sql, params)
                if consumable_result and consumable_result['total_consumable_cost']:
                    total_consumable_cost = float(consumable_result['total_consumable_cost'])
                else:
                    # 如果当月没有数据，使用所有耗材的平均成本作为模拟数据
                    all_consumable_sql = """
                    SELECT SUM(m.price * ma.quantity) * 0.1 as estimated_monthly_cost
                    FROM materials m
                    JOIN material_allocations ma ON m.id = ma.material_id
                    WHERE m.category = 'consumable'
                    AND ma.status = 'consumed'
                    """
                    if not user.is_admin():
                        all_consumable_sql += f" AND ma.department_id = {user.department_id}"

                    estimate_result = db.execute_query_one(all_consumable_sql)
                    if estimate_result and estimate_result['estimated_monthly_cost']:
                        total_consumable_cost = float(estimate_result['estimated_monthly_cost'])
                    else:
                        # 最后的备用方案：基于耗材总价值估算
                        backup_sql = """
                        SELECT SUM(price * quantity) * 0.05 as backup_estimate
                        FROM materials
                        WHERE category = 'consumable'
                        """
                        backup_result = db.execute_query_one(backup_sql)
                        if backup_result and backup_result['backup_estimate']:
                            total_consumable_cost = float(backup_result['backup_estimate'])
                        else:
                            total_consumable_cost = 180000.00  # 默认18万

            except Exception as e:
                print(f"获取耗材成本数据失败，使用默认值: {e}")
                total_consumable_cost = 180000.00  # 默认18万

            # 计算占比
            cash_flow_ratio = (total_consumable_cost / monthly_operating_cash_flow * 100) if monthly_operating_cash_flow > 0 else 0

            return {
                'monthly_operating_cash_flow': monthly_operating_cash_flow,
                'total_consumable_cost': total_consumable_cost,
                'cash_flow_ratio': round(cash_flow_ratio, 2),
                'current_year': current_year,
                'current_month': current_month
            }
        except Exception as e:
            print(f"计算耗材采购现金流占比失败: {e}")
            return {
                'monthly_operating_cash_flow': 3500000.00,
                'total_consumable_cost': 180000.00,
                'cash_flow_ratio': 5.14,
                'current_year': current_date.year,
                'current_month': current_date.month
            }

    def _calculate_department_asset_turnover(self, user):
        """计算科室资产周转率（物资使用次数 × 使用时长 / 物资原值，对比企业平均周转率）"""
        try:
            # 获取企业平均资产周转率
            current_date = date.today()
            current_year = current_date.year
            current_month = current_date.month

            company_turnover_sql = """
            SELECT average_asset_turnover_rate
            FROM company_financials
            WHERE year = %s AND month = %s
            """
            company_result = db.execute_query_one(company_turnover_sql, (current_year, current_month))

            if not company_result:
                # 如果没有当月数据，取最近一个月的数据
                company_turnover_sql = """
                SELECT average_asset_turnover_rate
                FROM company_financials
                ORDER BY year DESC, month DESC
                LIMIT 1
                """
                company_result = db.execute_query_one(company_turnover_sql)

            company_avg_turnover = float(company_result['average_asset_turnover_rate']) if company_result else 1.0

            # 获取科室资产数据
            if user.is_admin():
                # 管理员查看所有科室的数据
                dept_sql = """
                SELECT
                    d.id as department_id,
                    d.name as department_name,
                    SUM(m.price * ma.quantity) as total_asset_value,
                    SUM(m.usage_count * ma.quantity) as total_usage_count,
                    SUM(m.total_usage_hours * ma.quantity) as total_usage_hours
                FROM departments d
                LEFT JOIN material_allocations ma ON d.id = ma.department_id
                LEFT JOIN materials m ON ma.material_id = m.id AND m.category = 'fixed_asset'
                WHERE ma.status = 'allocated'
                GROUP BY d.id, d.name
                HAVING total_asset_value > 0
                """
                dept_results = db.execute_query(dept_sql)
            else:
                # 普通员工只查看本科室数据
                dept_sql = """
                SELECT
                    d.id as department_id,
                    d.name as department_name,
                    SUM(m.price * ma.quantity) as total_asset_value,
                    SUM(m.usage_count * ma.quantity) as total_usage_count,
                    SUM(m.total_usage_hours * ma.quantity) as total_usage_hours
                FROM departments d
                LEFT JOIN material_allocations ma ON d.id = ma.department_id
                LEFT JOIN materials m ON ma.material_id = m.id AND m.category = 'fixed_asset'
                WHERE ma.status = 'allocated' AND d.id = %s
                GROUP BY d.id, d.name
                HAVING total_asset_value > 0
                """
                dept_results = db.execute_query(dept_sql, (user.department_id,))

            department_turnovers = []

            for dept in dept_results:
                total_asset_value = float(dept['total_asset_value'] or 0)
                total_usage_count = int(dept['total_usage_count'] or 0)
                total_usage_hours = float(dept['total_usage_hours'] or 0)

                # 计算科室资产周转率
                # 公式：(使用次数 × 使用时长) / 资产原值
                if total_asset_value > 0:
                    turnover_rate = (total_usage_count * total_usage_hours) / total_asset_value

                    # 与企业平均周转率对比
                    vs_company_avg = (turnover_rate / company_avg_turnover * 100) if company_avg_turnover > 0 else 0

                    department_turnovers.append({
                        'department_id': dept['department_id'],
                        'department_name': dept['department_name'],
                        'total_asset_value': total_asset_value,
                        'total_usage_count': total_usage_count,
                        'total_usage_hours': total_usage_hours,
                        'turnover_rate': round(turnover_rate, 4),
                        'vs_company_avg': round(vs_company_avg, 2)
                    })

            return {
                'company_avg_turnover': company_avg_turnover,
                'department_turnovers': department_turnovers,
                'current_year': current_year,
                'current_month': current_month
            }
        except Exception as e:
            print(f"计算科室资产周转率失败: {e}")
            return {
                'company_avg_turnover': 1.0,
                'department_turnovers': [],
                'current_year': current_date.year,
                'current_month': current_date.month
            }

    def generate_financial_report_excel(self, user):
        """生成金融分析报表Excel"""
        try:
            # 获取金融统计数据
            financial_stats = self.get_financial_statistics(user)

            # 创建工作簿
            wb = openpyxl.Workbook()

            # 删除默认工作表
            wb.remove(wb.active)

            # 创建固定资产净值分析工作表
            self._create_fixed_asset_sheet(wb, financial_stats['fixed_asset_net_value_ratio'])

            # 创建现金流分析工作表
            self._create_cash_flow_sheet(wb, financial_stats['consumable_cash_flow_ratio'])

            # 创建资产周转率分析工作表
            self._create_turnover_sheet(wb, financial_stats['department_asset_turnover'])

            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            return output
        except Exception as e:
            print(f"生成金融报表Excel失败: {e}")
            raise

    def _create_fixed_asset_sheet(self, wb, data):
        """创建固定资产净值分析工作表"""
        ws = wb.create_sheet("固定资产净值分析")

        # 设置标题
        ws['A1'] = "固定资产净值分析报表"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:E1')

        # 设置表头
        headers = ['指标', '金额(元)', '占比(%)', '说明', '']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # 填充数据
        rows = [
            ['资产原值', f"{data.get('total_original_value', 0):,.2f}", '', '固定资产购置时的原始价值'],
            ['累计折旧', f"{data.get('total_accumulated_depreciation', 0):,.2f}", f"{data.get('depreciation_ratio', 0):.2f}%", '已计提的折旧总额'],
            ['资产净值', f"{data.get('total_net_value', 0):,.2f}", f"{data.get('net_value_ratio', 0):.2f}%", '扣除累计折旧后的净值'],
        ]

        for row_idx, row_data in enumerate(rows, 4):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 调整列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 20
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 30

    def _create_cash_flow_sheet(self, wb, data):
        """创建现金流分析工作表"""
        ws = wb.create_sheet("耗材采购现金流分析")

        # 设置标题
        ws['A1'] = f"耗材采购现金流分析报表 ({data.get('current_year', 2024)}年{data.get('current_month', 1)}月)"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:D1')

        # 设置表头
        headers = ['指标', '金额(元)', '占比(%)', '说明']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # 填充数据
        rows = [
            ['月度经营现金流', f"{data.get('monthly_operating_cash_flow', 0):,.2f}", '100.00%', '企业当月经营活动产生的现金流'],
            ['耗材采购支出', f"{data.get('total_consumable_cost', 0):,.2f}", f"{data.get('cash_flow_ratio', 0):.2f}%", '当月耗材采购的现金支出'],
        ]

        for row_idx, row_data in enumerate(rows, 4):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 调整列宽
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 20
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 35

    def _create_turnover_sheet(self, wb, data):
        """创建资产周转率分析工作表"""
        ws = wb.create_sheet("科室资产周转率分析")

        # 设置标题
        ws['A1'] = f"科室资产周转率分析报表 ({data.get('current_year', 2024)}年{data.get('current_month', 1)}月)"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:G1')

        # 企业平均周转率信息
        ws['A3'] = f"企业平均资产周转率: {data.get('company_avg_turnover', 1.0):.4f}"
        ws['A3'].font = Font(bold=True)
        ws.merge_cells('A3:G3')

        # 设置表头
        headers = ['科室名称', '资产原值(元)', '使用次数', '使用时长(小时)', '周转率', '对比企业平均(%)', '评价']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=5, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # 填充科室数据
        department_turnovers = data.get('department_turnovers', [])
        for row_idx, dept in enumerate(department_turnovers, 6):
            ws.cell(row=row_idx, column=1, value=dept['department_name'])
            ws.cell(row=row_idx, column=2, value=f"{dept['total_asset_value']:,.2f}")
            ws.cell(row=row_idx, column=3, value=dept['total_usage_count'])
            ws.cell(row=row_idx, column=4, value=f"{dept['total_usage_hours']:,.2f}")
            ws.cell(row=row_idx, column=5, value=f"{dept['turnover_rate']:.4f}")
            ws.cell(row=row_idx, column=6, value=f"{dept['vs_company_avg']:.2f}%")

            # 评价
            vs_avg = dept['vs_company_avg']
            if vs_avg >= 120:
                evaluation = "优秀"
                color = "00FF00"  # 绿色
            elif vs_avg >= 100:
                evaluation = "良好"
                color = "FFFF00"  # 黄色
            elif vs_avg >= 80:
                evaluation = "一般"
                color = "FFA500"  # 橙色
            else:
                evaluation = "需改进"
                color = "FF0000"  # 红色

            eval_cell = ws.cell(row=row_idx, column=7, value=evaluation)
            eval_cell.fill = PatternFill(start_color=color, end_color=color, fill_type="solid")

        # 调整列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 18
        ws.column_dimensions['C'].width = 12
        ws.column_dimensions['D'].width = 18
        ws.column_dimensions['E'].width = 12
        ws.column_dimensions['F'].width = 18
        ws.column_dimensions['G'].width = 12
