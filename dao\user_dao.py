from dao.database import db
from models.user import User
from datetime import datetime

class UserDAO:
    def __init__(self):
        self.db = db
    
    def create_user(self, user):
        """创建用户"""
        sql = """
        INSERT INTO users (username, password, real_name, role, department_id, email, phone, status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (user.username, user.password, user.real_name, user.role, 
                 user.department_id, user.email, user.phone, user.status)
        user_id = self.db.execute_insert(sql, params)
        user.id = user_id
        return user
    
    def get_user_by_id(self, user_id):
        """根据ID获取用户"""
        sql = "SELECT * FROM users WHERE id = %s"
        result = self.db.execute_query_one(sql, (user_id,))
        return User.from_dict(result) if result else None
    
    def get_user_by_username(self, username):
        """根据用户名获取用户"""
        sql = "SELECT * FROM users WHERE username = %s"
        result = self.db.execute_query_one(sql, (username,))
        return User.from_dict(result) if result else None
    
    def get_users_by_department(self, department_id):
        """根据科室ID获取用户列表"""
        sql = "SELECT * FROM users WHERE department_id = %s"
        results = self.db.execute_query(sql, (department_id,))
        return [User.from_dict(row) for row in results]

    def get_all_users(self):
        """获取所有用户"""
        sql = "SELECT * FROM users ORDER BY created_at DESC"
        results = self.db.execute_query(sql)
        return [User.from_dict(row) for row in results]
    
    def update_user(self, user):
        """更新用户信息"""
        sql = """
        UPDATE users SET real_name = %s, role = %s, department_id = %s, 
               email = %s, phone = %s, status = %s, updated_at = %s
        WHERE id = %s
        """
        params = (user.real_name, user.role, user.department_id, user.email, 
                 user.phone, user.status, datetime.now(), user.id)
        return self.db.execute_update(sql, params)
    
    def update_password(self, user_id, new_password):
        """更新用户密码"""
        sql = "UPDATE users SET password = %s, updated_at = %s WHERE id = %s"
        params = (new_password, datetime.now(), user_id)
        return self.db.execute_update(sql, params)
    
    def delete_user(self, user_id):
        """删除用户（硬删除）"""
        sql = "DELETE FROM users WHERE id = %s"
        return self.db.execute_update(sql, (user_id,))
    
    def authenticate_user(self, username, password):
        """用户认证"""
        user = self.get_user_by_username(username)
        if user and user.is_active():
            # 支持明文密码验证（测试环境）
            if user.password == password:
                return user

            # 兼容哈希密码验证
            import hashlib

            # 支持SHA256哈希
            password_sha256 = hashlib.sha256(password.encode()).hexdigest()
            if user.password == password_sha256:
                return user

            # 支持MD5哈希（兼容旧数据）
            password_md5 = hashlib.md5(password.encode()).hexdigest()
            if user.password == password_md5:
                return user
        return None
