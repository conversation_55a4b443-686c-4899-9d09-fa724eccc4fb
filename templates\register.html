<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 金融企业物资管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="login-container">
        <div class="login-card" style="max-width: 500px;">
            <h1 class="login-title">用户注册</h1>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST" id="registerForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="username" class="form-label">用户名 <span class="required">*</span></label>
                            <input type="text" id="username" name="username" class="form-control" required>
                            <small class="form-text">用于登录系统，至少3位字符</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="real_name" class="form-label">真实姓名 <span class="required">*</span></label>
                            <input type="text" id="real_name" name="real_name" class="form-control" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password" class="form-label">密码 <span class="required">*</span></label>
                            <input type="password" id="password" name="password" class="form-control" required>
                            <small class="form-text">至少6位字符</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="confirm_password" class="form-label">确认密码 <span class="required">*</span></label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="department_id" class="form-label">所属科室</label>
                    <select id="department_id" name="department_id" class="form-control">
                        <option value="">请选择科室（可选）</option>
                        {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">邮箱</label>
                            <input type="email" id="email" name="email" class="form-control">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone" class="form-label">电话</label>
                            <input type="tel" id="phone" name="phone" class="form-control">
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 1rem;">注册</button>
            </form>
            
            <div style="margin-top: 1.5rem; text-align: center;">
                <p>已有账号？<a href="{{ url_for('auth.login') }}" style="color: var(--primary-color); text-decoration: none;">立即登录</a></p>
            </div>
        </div>
    </div>

    <script>
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        var password = document.getElementById('password').value;
        var confirmPassword = document.getElementById('confirm_password').value;
        
        // 验证密码
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('两次输入的密码不一致！');
            return false;
        }
        
        if (password.length < 6) {
            e.preventDefault();
            alert('密码长度至少6位！');
            return false;
        }
        
        // 验证用户名
        var username = document.getElementById('username').value;
        if (username.length < 3) {
            e.preventDefault();
            alert('用户名长度至少3位！');
            return false;
        }
        
        // 验证真实姓名
        var realName = document.getElementById('real_name').value;
        if (realName.trim().length < 2) {
            e.preventDefault();
            alert('请输入有效的真实姓名！');
            return false;
        }
    });
    </script>
</body>
</html>
