#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试推荐API端点
"""

import requests
import json

def test_recommendations_api():
    """测试推荐API"""
    try:
        base_url = "http://localhost:5000"
        
        print("=== 测试推荐API端点 ===")
        
        # 测试生成推荐API
        print("1. 测试生成推荐API...")
        
        # 创建一个session来模拟登录
        session = requests.Session()
        
        # 先登录管理员账户
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code == 200 or login_response.status_code == 302:
            print("✅ 管理员登录成功")
            
            # 测试生成推荐
            generate_response = session.post(f"{base_url}/agent/api/generate-recommendations")
            print(f"生成推荐状态码: {generate_response.status_code}")
            
            if generate_response.status_code == 200:
                result = generate_response.json()
                print(f"生成推荐结果: {result}")
            else:
                print(f"生成推荐失败: {generate_response.text}")
            
            # 测试访问推荐页面
            recommendations_response = session.get(f"{base_url}/agent/recommendations")
            print(f"推荐页面状态码: {recommendations_response.status_code}")
            
            if recommendations_response.status_code == 200:
                print("✅ 推荐页面访问成功")
                # 检查页面内容是否包含管理员推荐相关内容
                content = recommendations_response.text
                if "管理建议" in content or "库存预警" in content or "审批提醒" in content:
                    print("✅ 页面包含管理员推荐内容")
                else:
                    print("⚠️ 页面可能不包含管理员推荐内容")
            else:
                print(f"推荐页面访问失败: {recommendations_response.status_code}")
        else:
            print(f"❌ 管理员登录失败: {login_response.status_code}")
            print(f"响应内容: {login_response.text[:200]}...")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_recommendations_api()
