#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import random

def generate_test_recommendations():
    """生成测试推荐数据"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='qyf20031211',
            database='goods'
        )
        cursor = conn.cursor()
        
        print("=== 生成测试推荐数据 ===")
        
        # 获取所有用户
        cursor.execute("SELECT id, username FROM users")
        users = cursor.fetchall()
        print(f"找到 {len(users)} 个用户")
        
        # 获取所有可用物资
        cursor.execute("SELECT id, name, category FROM materials WHERE status = 'available' AND remaining_quantity > 0")
        materials = cursor.fetchall()
        print(f"找到 {len(materials)} 个可用物资")
        
        # 为每个用户生成推荐
        recommendations = []
        for user in users:
            user_id = user[0]
            username = user[1]
            
            # 随机选择5-8个物资进行推荐
            selected_materials = random.sample(materials, min(random.randint(5, 8), len(materials)))
            
            for material in selected_materials:
                material_id = material[0]
                material_name = material[1]
                category = material[2]
                
                # 生成推荐评分和类型
                score = round(random.uniform(0.3, 0.9), 2)
                rec_type = random.choice(['department', 'global', 'personal'])
                
                # 生成推荐理由
                reasons = {
                    'department': f"您的科室同事经常申请{material_name}",
                    'global': f"{material_name}是最近的热门申请物资",
                    'personal': f"基于您的历史申请记录，推荐{material_name}"
                }
                reason = reasons[rec_type]
                
                recommendations.append((user_id, material_id, score, reason, rec_type))
        
        print(f"生成了 {len(recommendations)} 条推荐记录")
        
        # 批量插入推荐数据
        insert_sql = """
        INSERT INTO user_recommendations (user_id, material_id, score, reason, type)
        VALUES (%s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        score = VALUES(score),
        reason = VALUES(reason),
        type = VALUES(type),
        updated_at = CURRENT_TIMESTAMP
        """
        
        cursor.executemany(insert_sql, recommendations)
        conn.commit()
        
        print("✅ 推荐数据插入成功")
        
        # 验证插入结果
        cursor.execute("SELECT COUNT(*) FROM user_recommendations")
        count = cursor.fetchone()[0]
        print(f"总推荐记录数: {count}")
        
        # 显示每个用户的推荐数量
        cursor.execute("""
        SELECT u.username, COUNT(ur.id) as rec_count
        FROM users u
        LEFT JOIN user_recommendations ur ON u.id = ur.user_id
        GROUP BY u.id, u.username
        ORDER BY rec_count DESC
        """)
        user_stats = cursor.fetchall()
        print("\n各用户推荐数量:")
        for stat in user_stats:
            print(f"  {stat[0]}: {stat[1]} 条推荐")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 生成推荐数据失败: {e}")

if __name__ == '__main__':
    generate_test_recommendations()
