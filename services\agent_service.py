import json
from typing import Dict, List, Any
from dao.database import db


class AgentService:
    """Agent服务类"""

    def __init__(self):
        self.db = db
        self.scheduler = None

    def set_scheduler(self, scheduler):
        """设置调度器引用"""
        self.scheduler = scheduler

    def get_agent_status(self) -> Dict[str, Any]:
        """获取Agent状态"""
        if self.scheduler:
            return self.scheduler.get_agent_status()
        return {'error': 'Agent调度器未初始化'}

    def manual_run_agent(self, agent_name: str) -> Dict[str, Any]:
        """手动执行Agent"""
        if self.scheduler:
            return self.scheduler.manual_run_agent(agent_name)
        return {'error': 'Agent调度器未初始化'}

    def get_execution_history(self, agent_name: str = None, limit: int = 50) -> List[Dict]:
        """获取执行历史"""
        if self.scheduler:
            return self.scheduler.get_execution_history(agent_name, limit)
        return []

    def get_agent_statistics(self) -> Dict[str, Any]:
        """获取Agent统计信息"""
        if self.scheduler:
            return self.scheduler.get_agent_statistics()
        return {}

    def get_user_recommendations(self, user_id: int, limit: int = 5) -> List[Dict]:
        """获取用户推荐（管理员和普通员工不同内容）"""
        try:
            # 首先检查用户角色
            user_sql = "SELECT role, department_id FROM users WHERE id = %s"
            user_result = self.db.execute_query(user_sql, (user_id,))

            if not user_result:
                return []

            user_info = user_result[0]
            if user_info.get('role') == 'admin':
                # 管理员获取管理类推荐
                return self.get_admin_recommendations(user_id, limit)

            department_id = user_info.get('department_id')
            if not department_id:
                return []

            # 获取推荐，确保物资对用户科室可用
            sql = """
            SELECT ur.id, ur.user_id, ur.material_id, ur.score, ur.reason, ur.type,
                   ur.created_at, ur.updated_at,
                   m.name as material_name, m.category, m.remaining_quantity, m.price as unit_price
            FROM user_recommendations ur
            JOIN materials m ON ur.material_id = m.id
            WHERE ur.user_id = %s
            AND m.status = 'available'
            AND m.remaining_quantity > 0
            AND (
                m.id IN (
                    SELECT DISTINCT ma.material_id
                    FROM material_allocations ma
                    WHERE ma.department_id = %s
                )
                OR m.category = 'consumable'
            )
            ORDER BY ur.score DESC, ur.created_at DESC
            LIMIT %s
            """
            return self.db.execute_query(sql, (user_id, department_id, limit))
        except Exception as e:
            print(f"获取用户推荐失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def get_admin_recommendations(self, user_id: int, limit: int = 5) -> List[Dict]:
        """获取管理员推荐"""
        try:
            # 获取管理员推荐数据
            sql = """
            SELECT ur.id, ur.user_id, ur.material_id, ur.score, ur.reason, ur.type,
                   ur.created_at, ur.updated_at,
                   COALESCE(m.name, ur.reason) as material_name,
                   COALESCE(m.category, 'management') as category,
                   m.remaining_quantity, m.price as unit_price
            FROM user_recommendations ur
            LEFT JOIN materials m ON ur.material_id = m.id
            WHERE ur.user_id = %s
            ORDER BY ur.score DESC, ur.created_at DESC
            LIMIT %s
            """
            return self.db.execute_query(sql, (user_id, limit))
        except Exception as e:
            print(f"获取管理员推荐失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def get_analysis_data(self) -> Dict[str, Any]:
        """获取数据分析结果"""
        try:
            analysis_data = {}

            # 物资总数
            sql = "SELECT COUNT(*) as total FROM materials WHERE status = 'available'"
            result = self.db.execute_query(sql)
            analysis_data['total_materials'] = result[0]['total'] if result else 0

            # 申请总数
            sql = "SELECT COUNT(*) as total FROM material_requests"
            result = self.db.execute_query(sql)
            analysis_data['total_requests'] = result[0]['total'] if result else 0

            # 低库存物资数量
            sql = "SELECT COUNT(*) as total FROM materials WHERE remaining_quantity <= 10 AND status = 'available'"
            result = self.db.execute_query(sql)
            analysis_data['low_stock_count'] = result[0]['total'] if result else 0

            # 物资总价值
            sql = "SELECT SUM(price * remaining_quantity) as total_value FROM materials WHERE status = 'available'"
            result = self.db.execute_query(sql)
            analysis_data['total_value'] = float(result[0]['total_value'] or 0) if result else 0

            # 物资类别分布
            sql = """
            SELECT category, COUNT(*) as count
            FROM materials
            WHERE status = 'available'
            GROUP BY category
            """
            analysis_data['category_distribution'] = self.db.execute_query(sql)

            # 热门物资排行
            sql = """
            SELECT m.name, COUNT(mr.id) as request_count, SUM(mr.quantity) as total_quantity
            FROM materials m
            LEFT JOIN material_requests mr ON m.id = mr.material_id
            WHERE m.status = 'available'
            GROUP BY m.id, m.name
            HAVING request_count > 0
            ORDER BY request_count DESC, total_quantity DESC
            LIMIT 10
            """
            analysis_data['popular_materials'] = self.db.execute_query(sql)

            # 低库存物资
            sql = """
            SELECT name, remaining_quantity
            FROM materials
            WHERE remaining_quantity <= 10 AND status = 'available'
            ORDER BY remaining_quantity ASC
            LIMIT 20
            """
            analysis_data['low_stock_materials'] = self.db.execute_query(sql)

            # 用户活跃度分析
            sql = """
            SELECT u.username, COUNT(mr.id) as request_count,
                   SUM(mr.quantity) as total_quantity,
                   MAX(mr.created_at) as last_request_time
            FROM users u
            LEFT JOIN material_requests mr ON u.id = mr.user_id
            GROUP BY u.id, u.username
            ORDER BY request_count DESC
            LIMIT 20
            """
            analysis_data['user_activity'] = self.db.execute_query(sql)

            # 月度申请趋势（最近6个月）
            sql = """
            SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count
            FROM material_requests
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month ASC
            """
            analysis_data['monthly_trend'] = self.db.execute_query(sql)

            return analysis_data

        except Exception as e:
            print(f"获取分析数据失败: {e}")
            return {}

    def get_agent_config(self) -> Dict[str, Any]:
        """获取Agent配置"""
        try:
            # 确保配置表存在
            self._ensure_config_table()
            
            sql = "SELECT * FROM agent_config"
            configs = self.db.execute_query(sql)
            
            # 转换为字典格式
            config_dict = {}
            for config in configs:
                try:
                    config_dict[config['agent_name']] = json.loads(config['config_data'])
                except json.JSONDecodeError:
                    config_dict[config['agent_name']] = {}
            
            # 如果没有配置，返回默认配置
            if not config_dict:
                config_dict = self._get_default_config()
                
            return config_dict
            
        except Exception as e:
            print(f"获取Agent配置失败: {e}")
            return self._get_default_config()

    def update_agent_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新Agent配置"""
        try:
            # 确保配置表存在
            self._ensure_config_table()
            
            for agent_name, config in config_data.items():
                # 检查配置是否存在
                check_sql = "SELECT id FROM agent_config WHERE agent_name = %s"
                existing = self.db.execute_query_one(check_sql, (agent_name,))
                
                config_json = json.dumps(config, ensure_ascii=False)
                
                if existing:
                    # 更新现有配置
                    update_sql = """
                    UPDATE agent_config 
                    SET config_data = %s, updated_at = NOW() 
                    WHERE agent_name = %s
                    """
                    self.db.execute_update(update_sql, (config_json, agent_name))
                else:
                    # 插入新配置
                    insert_sql = """
                    INSERT INTO agent_config (agent_name, config_data, created_at, updated_at)
                    VALUES (%s, %s, NOW(), NOW())
                    """
                    self.db.execute_insert(insert_sql, (agent_name, config_json))
            
            return {'success': True, 'message': '配置更新成功'}
            
        except Exception as e:
            print(f"更新Agent配置失败: {e}")
            return {'success': False, 'error': str(e)}

    def _ensure_config_table(self):
        """确保配置表存在"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS agent_config (
                id INT AUTO_INCREMENT PRIMARY KEY,
                agent_name VARCHAR(100) NOT NULL UNIQUE,
                config_data JSON NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_agent_name (agent_name)
            )
            """
            self.db.execute_update(create_table_sql)
        except Exception as e:
            print(f"创建配置表失败: {e}")

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'InventoryAlert': {
                'low_stock_threshold': 10,
                'schedule_time': '08:00',
                'interval': 'daily',
                'enabled': True
            },
            'SmartRecommendation': {
                'max_recommendations': 5,
                'similarity_threshold': 0.1,
                'schedule_time': '22:00',
                'interval': 'daily',
                'enabled': True
            },
            'DataAnalysis': {
                'analysis_period_months': 3,
                'schedule_time': 'monday.09:00',
                'interval': 'weekly',
                'enabled': True
            }
        }

    def get_low_stock_alerts(self, limit: int = 20) -> List[Dict]:
        """获取低库存预警"""
        try:
            sql = """
            SELECT id, name, category, remaining_quantity, quantity, 
                   ROUND((remaining_quantity / quantity) * 100, 1) as stock_percentage
            FROM materials 
            WHERE remaining_quantity <= 10 
            AND status = 'available'
            ORDER BY remaining_quantity ASC
            LIMIT %s
            """
            return self.db.execute_query(sql, (limit,))
        except Exception as e:
            print(f"获取低库存预警失败: {e}")
            return []

    def get_usage_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取使用统计"""
        try:
            # 按类别统计
            category_sql = """
            SELECT m.category, COUNT(*) as request_count, SUM(mr.quantity) as total_quantity
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY m.category
            ORDER BY total_quantity DESC
            """
            category_stats = self.db.execute_query(category_sql, (days,))
            
            # 按科室统计
            department_sql = """
            SELECT d.name as department_name, COUNT(*) as request_count, SUM(mr.quantity) as total_quantity
            FROM material_requests mr
            JOIN users u ON mr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY d.id, d.name
            ORDER BY total_quantity DESC
            """
            department_stats = self.db.execute_query(department_sql, (days,))
            
            # 总体统计
            total_sql = """
            SELECT COUNT(*) as total_requests, SUM(quantity) as total_quantity
            FROM material_requests
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            """
            total_stats = self.db.execute_query_one(total_sql, (days,))
            
            return {
                'category_statistics': category_stats,
                'department_statistics': department_stats,
                'total_statistics': total_stats or {'total_requests': 0, 'total_quantity': 0}
            }
            
        except Exception as e:
            print(f"获取使用统计失败: {e}")
            return {
                'category_statistics': [],
                'department_statistics': [],
                'total_statistics': {'total_requests': 0, 'total_quantity': 0}
            }
