#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

def check_recommendation_data():
    """检查推荐算法所需的数据"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='lax217652',
            database='goods'
        )
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        print('=== 检查推荐算法所需数据 ===')

        # 检查用户数据
        cursor.execute('SELECT id, username, role, department_id FROM users LIMIT 5')
        users = cursor.fetchall()
        print(f'用户数量: {len(users)}')
        for user in users:
            print(f'  用户ID: {user["id"]}, 用户名: {user["username"]}, 角色: {user["role"]}, 科室ID: {user["department_id"]}')

        # 检查物资数据
        cursor.execute('SELECT COUNT(*) as count FROM materials')
        material_count = cursor.fetchone()
        print(f'物资总数: {material_count["count"]}')

        # 检查申请记录
        cursor.execute('SELECT COUNT(*) as count FROM material_requests')
        request_count = cursor.fetchone()
        print(f'申请记录总数: {request_count["count"]}')

        # 检查分配记录
        cursor.execute('SELECT COUNT(*) as count FROM material_allocations')
        allocation_count = cursor.fetchone()
        print(f'分配记录总数: {allocation_count["count"]}')

        # 检查推荐记录
        cursor.execute('SELECT COUNT(*) as count FROM user_recommendations')
        recommendation_count = cursor.fetchone()
        print(f'现有推荐记录总数: {recommendation_count["count"]}')

        # 检查科室数据
        cursor.execute('SELECT id, name FROM departments')
        departments = cursor.fetchall()
        print(f'科室数量: {len(departments)}')
        for dept in departments:
            print(f'  科室ID: {dept["id"]}, 科室名: {dept["name"]}')

        conn.close()
        return True

    except Exception as e:
        print(f'检查数据失败: {e}')
        return False

if __name__ == '__main__':
    check_recommendation_data()
