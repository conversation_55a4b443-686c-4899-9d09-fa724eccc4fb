-- 数据库升级脚本：添加金融功能
USE goods;

-- 为materials表添加金融相关字段
ALTER TABLE materials 
ADD COLUMN depreciation_method ENUM('straight_line', 'declining_balance', 'sum_of_years') DEFAULT 'straight_line' COMMENT '折旧方法：直线法/余额递减法/年数总和法',
ADD COLUMN useful_life_years INT DEFAULT 5 COMMENT '使用年限（年）',
ADD COLUMN residual_value DECIMAL(10,2) DEFAULT 0 COMMENT '残值',
ADD COLUMN accumulated_depreciation DECIMAL(10,2) DEFAULT 0 COMMENT '累计折旧',
ADD COLUMN usage_count INT DEFAULT 0 COMMENT '使用次数',
ADD COLUMN total_usage_hours DECIMAL(10,2) DEFAULT 0 COMMENT '总使用时长（小时）';

-- 创建企业财务数据表
CREATE TABLE IF NOT EXISTS company_financials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    year INT NOT NULL COMMENT '年份',
    month INT NOT NULL COMMENT '月份',
    monthly_operating_cash_flow DECIMAL(15,2) NOT NULL COMMENT '月度经营现金流',
    total_assets DECIMAL(15,2) NOT NULL COMMENT '总资产',
    average_asset_turnover_rate DECIMAL(5,4) DEFAULT 1.0 COMMENT '企业平均资产周转率',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_year_month (year, month)
) COMMENT='企业财务数据表';

-- 创建耗材采购现金流表
CREATE TABLE IF NOT EXISTS consumable_purchases (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_id INT NOT NULL COMMENT '耗材ID',
    purchase_date DATE NOT NULL COMMENT '采购日期',
    quantity INT NOT NULL COMMENT '采购数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_amount DECIMAL(12,2) NOT NULL COMMENT '总金额',
    supplier VARCHAR(200) COMMENT '供应商',
    payment_method ENUM('cash', 'bank_transfer', 'credit') DEFAULT 'bank_transfer' COMMENT '付款方式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE
) COMMENT='耗材采购现金流表';

-- 插入企业财务数据示例（2024年数据）
INSERT IGNORE INTO company_financials (year, month, monthly_operating_cash_flow, total_assets, average_asset_turnover_rate) VALUES
(2024, 1, 5000000.00, ********.00, 1.2),
(2024, 2, 4800000.00, ********.00, 1.15),
(2024, 3, 5200000.00, ********.00, 1.25),
(2024, 4, 4900000.00, ********.00, 1.18),
(2024, 5, 5300000.00, ********.00, 1.22),
(2024, 6, 5100000.00, ********.00, 1.20),
(2024, 7, 5400000.00, ********.00, 1.24),
(2024, 8, 5200000.00, ********.00, 1.21),
(2024, 9, 5500000.00, ********.00, 1.26),
(2024, 10, 5300000.00, ********.00, 1.23),
(2024, 11, 5600000.00, ********.00, 1.27),
(2024, 12, 5800000.00, ********.00, 1.30);

-- 为现有固定资产更新金融字段的默认值
UPDATE materials 
SET 
    useful_life_years = CASE 
        WHEN name LIKE '%电脑%' OR name LIKE '%笔记本%' THEN 3
        WHEN name LIKE '%打印机%' OR name LIKE '%复印机%' THEN 5
        WHEN name LIKE '%空调%' OR name LIKE '%投影仪%' THEN 8
        WHEN name LIKE '%办公桌%' OR name LIKE '%办公椅%' OR name LIKE '%文件柜%' THEN 10
        ELSE 5
    END,
    residual_value = price * 0.05,  -- 残值设为原价的5%
    accumulated_depreciation = CASE 
        WHEN category = 'fixed_asset' THEN 
            GREATEST(0, (price - price * 0.05) * DATEDIFF(CURDATE(), purchase_date) / 365 / useful_life_years)
        ELSE 0
    END
WHERE category = 'fixed_asset';

-- 创建索引
CREATE INDEX idx_company_financials_year_month ON company_financials(year, month);
CREATE INDEX idx_consumable_purchases_date ON consumable_purchases(purchase_date);
CREATE INDEX idx_consumable_purchases_material ON consumable_purchases(material_id);
