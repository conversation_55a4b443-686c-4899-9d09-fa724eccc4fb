# 表6-1 智能代理的三层模块

| 视图层 | | 控制器 | 模型 |
|--------|--------|--------|------|
| **HTML模板** | **表单组件** | **路由标签** | |
| agent_dashboard.html | AgentConfigForm | Flask Blueprint 标签 | AgentService |
| recommendations.html | RecommendationForm | Flask Schedule 标签 | SmartRecommendationAgent |
| agent_logs.html | AlertConfigForm | Agent API 标签 | InventoryAlertAgent |
| agent_config.html | AnalysisForm | 定时任务标签 | DataAnalysisAgent |
| alert_settings.html | ExecuteForm | 手动执行标签 | BaseAgent |
| analysis_report.html | LogFilterForm | 日志管理标签 | NotificationService |
| agent_status.html | | 状态监控标签 | AgentScheduler |
