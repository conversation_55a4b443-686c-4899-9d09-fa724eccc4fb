{% extends "base.html" %}

{% block title %}系统错误{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>系统错误
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-times-circle me-2"></i>操作失败
                        </h5>
                        <p class="mb-0">
                            {% if error %}
                                {{ error }}
                            {% else %}
                                系统遇到了一个未知错误，请稍后重试。
                            {% endif %}
                        </p>
                    </div>
                    
                    <div class="mt-4">
                        <h6>可能的解决方案：</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-primary me-2"></i>检查网络连接是否正常</li>
                            <li><i class="fas fa-arrow-right text-primary me-2"></i>刷新页面重试</li>
                            <li><i class="fas fa-arrow-right text-primary me-2"></i>如果问题持续存在，请联系系统管理员</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4 d-flex gap-2">
                        <button onclick="history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回上页
                        </button>
                        <a href="{{ url_for('material.dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-home me-1"></i>返回首页
                        </a>
                        <button onclick="location.reload()" class="btn btn-outline-primary">
                            <i class="fas fa-sync-alt me-1"></i>刷新页面
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}

.alert {
    border-radius: 8px;
}

.btn {
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.list-unstyled li {
    padding: 4px 0;
}

.text-primary {
    color: #007bff !important;
}
</style>
{% endblock %}
