#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为管理员创建管理类推荐数据
"""

import pymysql
from datetime import datetime, timedelta

def create_admin_recommendations():
    """为管理员创建管理类推荐"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='lax217652',
            database='goods'
        )
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        print("=== 创建管理员推荐数据 ===")
        
        # 1. 获取管理员用户
        cursor.execute("SELECT id, username FROM users WHERE role = 'admin'")
        admins = cursor.fetchall()
        print(f"找到 {len(admins)} 个管理员用户")
        
        for admin in admins:
            admin_id = admin['id']
            username = admin['username']
            print(f"\n为管理员 {username} (ID: {admin_id}) 创建推荐...")
            
            # 清除现有推荐
            cursor.execute("DELETE FROM user_recommendations WHERE user_id = %s", (admin_id,))
            
            # 2. 库存预警推荐
            print("  生成库存预警推荐...")
            cursor.execute("""
                SELECT m.id, m.name, m.remaining_quantity, m.quantity
                FROM materials m
                WHERE m.remaining_quantity <= (m.quantity * 0.2)
                AND m.status = 'available'
                AND m.category = 'consumable'
                ORDER BY (m.remaining_quantity / NULLIF(m.quantity, 0)) ASC
                LIMIT 5
            """)
            low_stock_materials = cursor.fetchall()

            for material in low_stock_materials:
                stock_ratio = material['remaining_quantity'] / max(material['quantity'], 1)
                reason = f"库存预警：{material['name']} 剩余库存 {material['remaining_quantity']} 已低于总量的20%（总量 {material['quantity']}）"
                score = 0.95 - stock_ratio * 0.2

                cursor.execute("""
                    INSERT INTO user_recommendations (user_id, material_id, reason, score, type, created_at)
                    VALUES (%s, %s, %s, %s, %s, NOW())
                """, (admin_id, material['id'], reason, min(score, 0.95), 'inventory_alert'))
            
            # 3. 审批提醒推荐
            print("  生成审批提醒推荐...")
            cursor.execute("""
                SELECT mr.material_id, m.name, COUNT(*) as pending_count,
                       MIN(mr.request_date) as earliest_request
                FROM material_requests mr
                JOIN materials m ON mr.material_id = m.id
                WHERE mr.status = 'pending'
                GROUP BY mr.material_id, m.name
                ORDER BY pending_count DESC, earliest_request ASC
                LIMIT 5
            """)
            pending_approvals = cursor.fetchall()
            
            for approval in pending_approvals:
                earliest_request = approval['earliest_request']
                if isinstance(earliest_request, datetime):
                    earliest_request = earliest_request.date()
                days_waiting = (datetime.now().date() - earliest_request).days
                reason = f"审批提醒：{approval['name']} 有 {approval['pending_count']} 个待审批申请，最早申请已等待 {days_waiting} 天"
                score = 0.90 + min(days_waiting * 0.01, 0.05)  # 等待时间越长分数越高
                
                cursor.execute("""
                    INSERT INTO user_recommendations (user_id, material_id, reason, score, type, created_at)
                    VALUES (%s, %s, %s, %s, %s, NOW())
                """, (admin_id, approval['material_id'], reason, min(score, 0.95), 'approval_reminder'))
            
            # 4. 热门申请分析推荐
            print("  生成热门申请分析推荐...")
            cursor.execute("""
                SELECT mr.material_id, m.name, COUNT(*) as request_count,
                       AVG(mr.quantity) as avg_quantity
                FROM material_requests mr
                JOIN materials m ON mr.material_id = m.id
                WHERE mr.request_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY mr.material_id, m.name
                ORDER BY request_count DESC
                LIMIT 5
            """)
            popular_materials = cursor.fetchall()
            
            for material in popular_materials:
                reason = f"热门分析：{material['name']} 近30天被申请 {material['request_count']} 次，平均申请量 {material['avg_quantity']:.1f}"
                score = 0.80 + min(material['request_count'] * 0.01, 0.10)
                
                cursor.execute("""
                    INSERT INTO user_recommendations (user_id, material_id, reason, score, type, created_at)
                    VALUES (%s, %s, %s, %s, %s, NOW())
                """, (admin_id, material['material_id'], reason, min(score, 0.90), 'popular_analysis'))
            
            # 5. 科室使用分析推荐
            print("  生成科室使用分析推荐...")
            cursor.execute("""
                SELECT mr.material_id, m.name, d.name as dept_name, COUNT(*) as dept_requests
                FROM material_requests mr
                JOIN materials m ON mr.material_id = m.id
                JOIN users u ON mr.user_id = u.id
                JOIN departments d ON u.department_id = d.id
                WHERE mr.request_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY mr.material_id, m.name, d.id, d.name
                HAVING dept_requests >= 2
                ORDER BY dept_requests DESC
                LIMIT 5
            """)
            dept_analysis = cursor.fetchall()
            
            for analysis in dept_analysis:
                reason = f"科室分析：{analysis['dept_name']} 对 {analysis['name']} 需求较高（近30天申请 {analysis['dept_requests']} 次）"
                score = 0.75 + min(analysis['dept_requests'] * 0.02, 0.10)
                
                cursor.execute("""
                    INSERT INTO user_recommendations (user_id, material_id, reason, score, type, created_at)
                    VALUES (%s, %s, %s, %s, %s, NOW())
                """, (admin_id, analysis['material_id'], reason, min(score, 0.85), 'department_analysis'))
            
            # 6. 财务分析推荐
            print("  生成财务分析推荐...")
            cursor.execute("""
                SELECT mr.material_id, m.name, SUM(mr.quantity * m.price) as total_cost
                FROM material_requests mr
                JOIN materials m ON mr.material_id = m.id
                WHERE mr.request_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                AND mr.status IN ('approved', 'completed')
                GROUP BY mr.material_id, m.name
                ORDER BY total_cost DESC
                LIMIT 5
            """)
            cost_analysis = cursor.fetchall()

            for cost in cost_analysis:
                reason = f"成本分析：{cost['name']} 近30天总成本 ¥{cost['total_cost']:.2f}，建议关注采购预算"
                score = 0.70 + min(float(cost['total_cost']) / 10000 * 0.1, 0.15)

                cursor.execute("""
                    INSERT INTO user_recommendations (user_id, material_id, reason, score, type, created_at)
                    VALUES (%s, %s, %s, %s, %s, NOW())
                """, (admin_id, cost['material_id'], reason, min(score, 0.85), 'cost_analysis'))
        
        conn.commit()
        
        # 验证创建的推荐
        cursor.execute("""
            SELECT u.username, COUNT(*) as rec_count
            FROM user_recommendations ur
            JOIN users u ON ur.user_id = u.id
            WHERE u.role = 'admin'
            GROUP BY u.id, u.username
        """)
        admin_recs = cursor.fetchall()
        
        print(f"\n=== 管理员推荐创建完成 ===")
        for rec in admin_recs:
            print(f"管理员 {rec['username']}: {rec['rec_count']} 条推荐")
        
        # 显示推荐类型分布
        cursor.execute("""
            SELECT ur.type, COUNT(*) as count
            FROM user_recommendations ur
            JOIN users u ON ur.user_id = u.id
            WHERE u.role = 'admin'
            GROUP BY ur.type
            ORDER BY count DESC
        """)
        type_distribution = cursor.fetchall()
        
        print(f"\n推荐类型分布:")
        for dist in type_distribution:
            print(f"  {dist['type']}: {dist['count']} 条")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 创建管理员推荐失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    create_admin_recommendations()
