#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查表结构
"""

import pymysql

def check_table_structure():
    """检查表结构"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='lax217652',
            database='goods',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        cursor = conn.cursor()
        
        print("=== user_recommendations表结构 ===")
        cursor.execute('DESCRIBE user_recommendations')
        columns = cursor.fetchall()
        
        for col in columns:
            field = col['Field']
            type_info = col['Type']
            null_info = 'NULL' if col['Null'] == 'YES' else 'NOT NULL'
            print(f"  {field}: {type_info} {null_info}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_structure()
