#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查管理员推荐数据
"""

import pymysql

def check_admin_recommendations():
    """检查管理员推荐"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='lax217652',
            database='goods',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        cursor = conn.cursor()
        
        print("=== 检查管理员推荐数据 ===")
        
        cursor.execute("""
            SELECT * FROM user_recommendations 
            WHERE user_id = 1 
            ORDER BY score DESC
        """)
        recs = cursor.fetchall()
        
        print(f"管理员推荐数量: {len(recs)}")
        print()
        
        for i, rec in enumerate(recs, 1):
            print(f"{i}. 类型: {rec['type']}")
            print(f"   评分: {rec['score']}")
            print(f"   物资ID: {rec['material_id']}")
            print(f"   原因: {rec['reason']}")
            print(f"   创建时间: {rec['created_at']}")
            print("---")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_admin_recommendations()
